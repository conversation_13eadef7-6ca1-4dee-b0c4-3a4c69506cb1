// Test script for Supabase Authentication
import { createClient } from '@supabase/supabase-js';

// Configuration from .env
const SUPABASE_URL = 'https://idvceughudfmupvoxgns.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlkdmNldWdodWRmbXVwdm94Z25zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MDgwMTEsImV4cCI6MjA2OTI4NDAxMX0.kMH7OCjSTW3mSml5QI45_-6NOnW1vXyCLZPHV6_pmmg';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlkdmNldWdodWRmbXVwdm94Z25zIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzcwODAxMSwiZXhwIjoyMDY5Mjg0MDExfQ.SItAasQ7LG3TGVEnxgY-8ksS-lP7Op3Daag_qaiZ-jM';

// Initialize Supabase clients
const supabaseAnon = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
const supabaseService = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testDatabaseConnection() {
  console.log('\n🔍 Testing Database Connection...\n');
  
  try {
    // Test basic connection with service role
    const { data, error } = await supabaseService
      .from('tenants')
      .select('id')
      .limit(1);
    
    if (error) {
      console.log(`❌ Database connection failed: ${error.message}`);
      return false;
    }
    
    console.log('✅ Database connection successful');
    console.log(`   Tenants table accessible: ${JSON.stringify(data)}`);
    return true;
  } catch (error) {
    console.log(`❌ Database connection error: ${error.message}`);
    return false;
  }
}

async function testCreateTenant() {
  console.log('\n🔍 Testing Tenant Creation...\n');
  
  try {
    // Create a test tenant
    const { data, error } = await supabaseService
      .from('tenants')
      .insert({
        name: 'Test Company',
        slug: 'test-company-' + Date.now(),
        plan: 'free',
        settings: {}
      })
      .select()
      .single();
    
    if (error) {
      console.log(`❌ Tenant creation failed: ${error.message}`);
      return null;
    }
    
    console.log('✅ Tenant created successfully');
    console.log(`   Tenant ID: ${data.id}`);
    console.log(`   Tenant Name: ${data.name}`);
    return data;
  } catch (error) {
    console.log(`❌ Tenant creation error: ${error.message}`);
    return null;
  }
}

async function testUserSignUp(tenant) {
  console.log('\n🔍 Testing User Sign Up...\n');
  
  if (!tenant) {
    console.log('❌ Cannot test user signup without tenant');
    return null;
  }
  
  try {
    const testEmail = `test.user.${Date.now()}@gmail.com`;
    const testPassword = 'TestPassword123!';
    
    // Sign up user
    const { data: authData, error: authError } = await supabaseAnon.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          tenant_id: tenant.id,
          full_name: 'Test User'
        }
      }
    });
    
    if (authError) {
      console.log(`❌ User signup failed: ${authError.message}`);
      return null;
    }
    
    console.log('✅ User signup successful');
    console.log(`   User ID: ${authData.user?.id}`);
    console.log(`   Email: ${authData.user?.email}`);
    console.log(`   Session: ${authData.session ? 'Available' : 'Not available (email confirmation required)'}`);

    return {
      user: authData.user,
      session: authData.session,
      tenant: tenant
    };
  } catch (error) {
    console.log(`❌ User signup error: ${error.message}`);
    return null;
  }
}

async function testUserProfile(userAuth) {
  console.log('\n🔍 Testing User Profile Creation...\n');
  
  if (!userAuth || !userAuth.user) {
    console.log('❌ Cannot test user profile without authenticated user');
    return false;
  }
  
  try {
    // Create user profile manually (since trigger might not work in test)
    const { data, error } = await supabaseService
      .from('user_profiles')
      .insert({
        id: userAuth.user.id,
        tenant_id: userAuth.tenant.id,
        email: userAuth.user.email,
        full_name: 'Test User',
        role: 'user',
        is_active: true,
        preferences: {}
      })
      .select()
      .single();
    
    if (error) {
      console.log(`❌ User profile creation failed: ${error.message}`);
      return false;
    }
    
    console.log('✅ User profile created successfully');
    console.log(`   Profile ID: ${data.id}`);
    console.log(`   Tenant ID: ${data.tenant_id}`);
    return true;
  } catch (error) {
    console.log(`❌ User profile creation error: ${error.message}`);
    return false;
  }
}

async function testJWTToken(userAuth) {
  console.log('\n🔍 Testing JWT Token Validation...\n');

  if (!userAuth || !userAuth.session) {
    console.log('❌ Cannot test JWT without session (email confirmation may be required)');

    // Try to create a session using service role for testing
    try {
      const { data: { session }, error } = await supabaseService.auth.admin.generateLink({
        type: 'magiclink',
        email: userAuth.user.email
      });

      if (!error && session) {
        console.log('✅ Generated test session for JWT validation');
        userAuth.session = session;
      } else {
        console.log('⚠️ Could not generate test session, skipping JWT test');
        return false;
      }
    } catch (e) {
      console.log('⚠️ Could not generate test session, skipping JWT test');
      return false;
    }
  }
  
  try {
    // Test token validation
    const { data: { user }, error } = await supabaseAnon.auth.getUser(userAuth.session.access_token);
    
    if (error) {
      console.log(`❌ JWT validation failed: ${error.message}`);
      return false;
    }
    
    console.log('✅ JWT token validation successful');
    console.log(`   User ID: ${user?.id}`);
    console.log(`   Email: ${user?.email}`);
    return true;
  } catch (error) {
    console.log(`❌ JWT validation error: ${error.message}`);
    return false;
  }
}

async function testRowLevelSecurity(userAuth) {
  console.log('\n🔍 Testing Row Level Security...\n');
  
  if (!userAuth || !userAuth.session) {
    console.log('❌ Cannot test RLS without authenticated session');
    return false;
  }
  
  try {
    // Create authenticated client
    const authClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      global: {
        headers: {
          Authorization: `Bearer ${userAuth.session.access_token}`
        }
      }
    });
    
    // Test accessing user's own profile
    const { data: profile, error: profileError } = await authClient
      .from('user_profiles')
      .select('*')
      .eq('id', userAuth.user.id)
      .single();
    
    if (profileError) {
      console.log(`❌ RLS test failed: ${profileError.message}`);
      return false;
    }
    
    console.log('✅ Row Level Security working');
    console.log(`   Can access own profile: ${profile?.email}`);
    
    // Test accessing other tenant's data (should fail)
    const { data: otherData, error: otherError } = await authClient
      .from('tenants')
      .select('*')
      .neq('id', userAuth.tenant.id)
      .limit(1);
    
    if (otherError) {
      console.log('✅ RLS properly blocking access to other tenant data');
    } else if (otherData && otherData.length === 0) {
      console.log('✅ RLS working - no unauthorized data returned');
    } else {
      console.log('⚠️ RLS might not be properly configured - got other tenant data');
    }
    
    return true;
  } catch (error) {
    console.log(`❌ RLS test error: ${error.message}`);
    return false;
  }
}

async function cleanup(userAuth) {
  console.log('\n🧹 Cleaning up test data...\n');
  
  try {
    if (userAuth && userAuth.user) {
      // Delete user profile
      await supabaseService
        .from('user_profiles')
        .delete()
        .eq('id', userAuth.user.id);
      
      // Delete user from auth
      await supabaseService.auth.admin.deleteUser(userAuth.user.id);
    }
    
    if (userAuth && userAuth.tenant) {
      // Delete tenant
      await supabaseService
        .from('tenants')
        .delete()
        .eq('id', userAuth.tenant.id);
    }
    
    console.log('✅ Cleanup completed');
  } catch (error) {
    console.log(`⚠️ Cleanup error: ${error.message}`);
  }
}

// Main test runner
async function runAuthTests() {
  console.log('🚀 Starting Authentication Test Suite');
  console.log('='.repeat(50));
  
  let userAuth = null;
  let tenant = null;
  
  try {
    // Test database connection
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) return;
    
    // Create test tenant
    tenant = await testCreateTenant();
    if (!tenant) return;
    
    // Test user signup
    userAuth = await testUserSignUp(tenant);
    if (!userAuth) return;
    
    // Test user profile creation
    const profileCreated = await testUserProfile(userAuth);
    if (!profileCreated) return;
    
    // Test JWT validation
    const jwtValid = await testJWTToken(userAuth);
    if (!jwtValid) return;
    
    // Test Row Level Security
    await testRowLevelSecurity(userAuth);
    
  } finally {
    // Always cleanup
    await cleanup(userAuth);
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ Authentication test suite completed!');
}

// Run the tests
runAuthTests().catch(console.error);
