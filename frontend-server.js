// Simple HTTP server to serve the frontend
import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.FRONTEND_PORT || 3001;

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// Serve components and other static assets
app.use('/components', express.static(path.join(__dirname, 'components')));
app.use('/styles', express.static(path.join(__dirname, 'styles')));

// Health check for frontend server
app.get('/frontend-health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Doc Maker App - Frontend Server',
    timestamp: new Date().toISOString(),
    port: PORT,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    pid: process.pid,
  });
});

// Catch-all handler: send back React app's index.html file for SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Frontend server error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong',
    timestamp: new Date().toISOString(),
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🌐 Frontend Server running on port ${PORT}`);
  console.log(`📱 Frontend URL: http://localhost:${PORT}`);
  console.log(`🔗 Backend URL: http://localhost:3000`);
  console.log(`📊 Frontend Health: http://localhost:${PORT}/frontend-health`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
  console.log('');
  console.log('🚀 Ready for manual testing!');
  console.log('   - Open http://localhost:3001 in your browser');
  console.log('   - Test backend connectivity using the interface');
  console.log('   - Verify Supabase Edge Functions are accessible');
});

export default app;
