// Production Monitoring Dashboard for Doc Maker App
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';

// Configuration
const config = {
  supabaseUrl: 'https://idvceughudfmupvoxgns.supabase.co',
  backendUrl: 'http://localhost:3000', // Update for production
  checkInterval: 30000, // 30 seconds
  logFile: 'logs/monitoring.log',
  alertThresholds: {
    responseTime: 5000, // 5 seconds
    errorRate: 10, // 10%
    uptime: 99, // 99%
  },
};

// Monitoring state
const monitoringState = {
  checks: {
    backend: { total: 0, errors: 0, responseTimes: [] },
    edgeFunctions: {
      'upload-template': { total: 0, errors: 0, responseTimes: [] },
      'create-report': { total: 0, errors: 0, responseTimes: [] },
      'document-processor': { total: 0, errors: 0, responseTimes: [] },
    },
    database: { total: 0, errors: 0, responseTimes: [] },
  },
  alerts: [],
  startTime: Date.now(),
};

// Utility functions
function log(level, message, data = {}) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    ...data,
  };
  
  console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`, data);
  
  // Write to log file
  const logDir = path.dirname(config.logFile);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  fs.appendFileSync(config.logFile, JSON.stringify(logEntry) + '\n');
}

function calculateStats(checks) {
  const total = checks.total;
  const errors = checks.errors;
  const responseTimes = checks.responseTimes;
  
  return {
    total,
    errors,
    errorRate: total > 0 ? (errors / total) * 100 : 0,
    uptime: total > 0 ? ((total - errors) / total) * 100 : 100,
    avgResponseTime: responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0,
    maxResponseTime: responseTimes.length > 0 ? Math.max(...responseTimes) : 0,
    minResponseTime: responseTimes.length > 0 ? Math.min(...responseTimes) : 0,
  };
}

function addAlert(type, message, severity = 'warning') {
  const alert = {
    timestamp: new Date().toISOString(),
    type,
    message,
    severity,
  };
  
  monitoringState.alerts.push(alert);
  
  // Keep only last 100 alerts
  if (monitoringState.alerts.length > 100) {
    monitoringState.alerts = monitoringState.alerts.slice(-100);
  }
  
  log('alert', `${severity.toUpperCase()}: ${message}`, { type, severity });
}

// Health check functions
async function checkBackendHealth() {
  const startTime = Date.now();
  const checks = monitoringState.checks.backend;
  
  try {
    const response = await fetch(`${config.backendUrl}/health`, {
      timeout: 10000,
    });
    
    const responseTime = Date.now() - startTime;
    checks.total++;
    checks.responseTimes.push(responseTime);
    
    // Keep only last 100 response times
    if (checks.responseTimes.length > 100) {
      checks.responseTimes = checks.responseTimes.slice(-100);
    }
    
    if (!response.ok) {
      checks.errors++;
      addAlert('backend', `Backend health check failed: ${response.status}`, 'error');
      return false;
    }
    
    const data = await response.json();
    
    // Check response time threshold
    if (responseTime > config.alertThresholds.responseTime) {
      addAlert('backend', `Backend response time high: ${responseTime}ms`, 'warning');
    }
    
    log('info', 'Backend health check passed', {
      responseTime,
      status: response.status,
      uptime: data.uptime,
    });
    
    return true;
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    checks.total++;
    checks.errors++;
    checks.responseTimes.push(responseTime);
    
    addAlert('backend', `Backend health check error: ${error.message}`, 'error');
    log('error', 'Backend health check failed', { error: error.message, responseTime });
    
    return false;
  }
}

async function checkEdgeFunctionHealth(functionName) {
  const startTime = Date.now();
  const checks = monitoringState.checks.edgeFunctions[functionName];
  
  try {
    const response = await fetch(`${config.supabaseUrl}/functions/v1/${functionName}`, {
      method: 'GET',
      timeout: 10000,
    });
    
    const responseTime = Date.now() - startTime;
    checks.total++;
    checks.responseTimes.push(responseTime);
    
    // Keep only last 100 response times
    if (checks.responseTimes.length > 100) {
      checks.responseTimes = checks.responseTimes.slice(-100);
    }
    
    // Edge functions should return 401 for unauthorized requests (expected)
    if (response.status === 401) {
      log('info', `Edge function ${functionName} health check passed`, {
        responseTime,
        status: response.status,
      });
      return true;
    }
    
    if (!response.ok && response.status !== 401) {
      checks.errors++;
      addAlert('edge-function', `Edge function ${functionName} health check failed: ${response.status}`, 'error');
      return false;
    }
    
    // Check response time threshold
    if (responseTime > config.alertThresholds.responseTime) {
      addAlert('edge-function', `Edge function ${functionName} response time high: ${responseTime}ms`, 'warning');
    }
    
    return true;
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    checks.total++;
    checks.errors++;
    checks.responseTimes.push(responseTime);
    
    addAlert('edge-function', `Edge function ${functionName} health check error: ${error.message}`, 'error');
    log('error', `Edge function ${functionName} health check failed`, { 
      error: error.message, 
      responseTime 
    });
    
    return false;
  }
}

async function checkDatabaseHealth() {
  const startTime = Date.now();
  const checks = monitoringState.checks.database;
  
  try {
    // Simple database health check via Supabase REST API
    const response = await fetch(`${config.supabaseUrl}/rest/v1/`, {
      method: 'GET',
      headers: {
        'apikey': process.env.SUPABASE_ANON_KEY || '',
      },
      timeout: 10000,
    });
    
    const responseTime = Date.now() - startTime;
    checks.total++;
    checks.responseTimes.push(responseTime);
    
    // Keep only last 100 response times
    if (checks.responseTimes.length > 100) {
      checks.responseTimes = checks.responseTimes.slice(-100);
    }
    
    if (!response.ok) {
      checks.errors++;
      addAlert('database', `Database health check failed: ${response.status}`, 'error');
      return false;
    }
    
    // Check response time threshold
    if (responseTime > config.alertThresholds.responseTime) {
      addAlert('database', `Database response time high: ${responseTime}ms`, 'warning');
    }
    
    log('info', 'Database health check passed', {
      responseTime,
      status: response.status,
    });
    
    return true;
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    checks.total++;
    checks.errors++;
    checks.responseTimes.push(responseTime);
    
    addAlert('database', `Database health check error: ${error.message}`, 'error');
    log('error', 'Database health check failed', { error: error.message, responseTime });
    
    return false;
  }
}

// Main monitoring function
async function runHealthChecks() {
  log('info', 'Running health checks...');
  
  const results = {
    backend: await checkBackendHealth(),
    database: await checkDatabaseHealth(),
    edgeFunctions: {},
  };
  
  // Check all edge functions
  for (const functionName of Object.keys(monitoringState.checks.edgeFunctions)) {
    results.edgeFunctions[functionName] = await checkEdgeFunctionHealth(functionName);
  }
  
  // Generate summary
  const summary = {
    timestamp: new Date().toISOString(),
    uptime: Date.now() - monitoringState.startTime,
    results,
    stats: {
      backend: calculateStats(monitoringState.checks.backend),
      database: calculateStats(monitoringState.checks.database),
      edgeFunctions: {},
    },
    recentAlerts: monitoringState.alerts.slice(-5),
  };
  
  // Calculate edge function stats
  for (const [functionName, checks] of Object.entries(monitoringState.checks.edgeFunctions)) {
    summary.stats.edgeFunctions[functionName] = calculateStats(checks);
  }
  
  log('info', 'Health check summary', summary);
  
  return summary;
}

// Display dashboard
function displayDashboard() {
  console.clear();
  console.log('🔍 Doc Maker App - Production Monitoring Dashboard');
  console.log('='.repeat(60));
  console.log(`Started: ${new Date(monitoringState.startTime).toISOString()}`);
  console.log(`Uptime: ${Math.floor((Date.now() - monitoringState.startTime) / 1000)}s`);
  console.log('');
  
  // Backend stats
  const backendStats = calculateStats(monitoringState.checks.backend);
  console.log('🖥️  Backend Server:');
  console.log(`   Status: ${backendStats.uptime >= config.alertThresholds.uptime ? '✅' : '❌'} ${backendStats.uptime.toFixed(1)}% uptime`);
  console.log(`   Checks: ${backendStats.total} (${backendStats.errors} errors)`);
  console.log(`   Response: ${backendStats.avgResponseTime.toFixed(0)}ms avg, ${backendStats.maxResponseTime}ms max`);
  console.log('');
  
  // Database stats
  const dbStats = calculateStats(monitoringState.checks.database);
  console.log('🗄️  Database:');
  console.log(`   Status: ${dbStats.uptime >= config.alertThresholds.uptime ? '✅' : '❌'} ${dbStats.uptime.toFixed(1)}% uptime`);
  console.log(`   Checks: ${dbStats.total} (${dbStats.errors} errors)`);
  console.log(`   Response: ${dbStats.avgResponseTime.toFixed(0)}ms avg, ${dbStats.maxResponseTime}ms max`);
  console.log('');
  
  // Edge functions stats
  console.log('⚡ Edge Functions:');
  for (const [functionName, checks] of Object.entries(monitoringState.checks.edgeFunctions)) {
    const stats = calculateStats(checks);
    console.log(`   ${functionName}:`);
    console.log(`     Status: ${stats.uptime >= config.alertThresholds.uptime ? '✅' : '❌'} ${stats.uptime.toFixed(1)}% uptime`);
    console.log(`     Checks: ${stats.total} (${stats.errors} errors)`);
    console.log(`     Response: ${stats.avgResponseTime.toFixed(0)}ms avg, ${stats.maxResponseTime}ms max`);
  }
  console.log('');
  
  // Recent alerts
  if (monitoringState.alerts.length > 0) {
    console.log('🚨 Recent Alerts:');
    monitoringState.alerts.slice(-5).forEach(alert => {
      const icon = alert.severity === 'error' ? '❌' : '⚠️';
      console.log(`   ${icon} ${alert.timestamp}: ${alert.message}`);
    });
    console.log('');
  }
  
  console.log(`Next check in ${config.checkInterval / 1000}s...`);
}

// Start monitoring
async function startMonitoring() {
  log('info', 'Starting production monitoring', {
    checkInterval: config.checkInterval,
    supabaseUrl: config.supabaseUrl,
    backendUrl: config.backendUrl,
  });
  
  // Initial health check
  await runHealthChecks();
  displayDashboard();
  
  // Set up periodic checks
  setInterval(async () => {
    await runHealthChecks();
    displayDashboard();
  }, config.checkInterval);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('info', 'Monitoring stopped by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('info', 'Monitoring stopped by system');
  process.exit(0);
});

// Start monitoring
startMonitoring().catch(error => {
  log('error', 'Failed to start monitoring', { error: error.message });
  process.exit(1);
});
