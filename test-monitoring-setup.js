// Test script for Production Monitoring Setup
import fs from 'fs';
import path from 'path';

// Helper function to check if file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Helper function to read file content
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    return null;
  }
}

// Helper function to check if content contains specific patterns
function containsPattern(content, pattern) {
  return new RegExp(pattern, 'i').test(content);
}

async function testLoggingSetup() {
  console.log('\n🔍 Testing Logging Setup...\n');
  
  const loggingFiles = [
    'src/utils/logger.ts',
    'src/utils/errorHandler.ts',
    'src/middleware/monitoring.ts'
  ];
  
  let allLoggingFilesExist = true;
  
  for (const file of loggingFiles) {
    const exists = fileExists(file);
    console.log(`${exists ? '✅' : '❌'} ${file}: ${exists ? 'Found' : 'Missing'}`);
    
    if (exists) {
      const content = readFile(file);
      if (content) {
        // Check for Winston integration
        const hasWinston = containsPattern(content, 'winston');
        console.log(`   ${hasWinston ? '✅' : '❌'} Winston integration: ${hasWinston}`);
        
        // Check for log levels
        const hasLogLevels = containsPattern(content, 'error|warn|info|debug');
        console.log(`   ${hasLogLevels ? '✅' : '❌'} Log levels defined: ${hasLogLevels}`);
        
        // Check for file transport
        const hasFileTransport = containsPattern(content, 'File|filename');
        console.log(`   ${hasFileTransport ? '✅' : '❌'} File transport: ${hasFileTransport}`);
      }
    } else {
      allLoggingFilesExist = false;
    }
  }
  
  return allLoggingFilesExist;
}

async function testMonitoringMiddleware() {
  console.log('\n🔍 Testing Monitoring Middleware...\n');
  
  const middlewareFile = 'src/middleware/monitoring.ts';
  
  if (fileExists(middlewareFile)) {
    const content = readFile(middlewareFile);
    
    const checks = [
      { name: 'Request timing middleware', pattern: 'requestTiming' },
      { name: 'Metrics collection', pattern: 'collectMetrics' },
      { name: 'Health check handler', pattern: 'healthCheck' },
      { name: 'Error tracking', pattern: 'errorTracking' },
      { name: 'Performance monitoring', pattern: 'performanceMonitor' },
      { name: 'Database health check', pattern: 'checkDatabaseHealth' },
      { name: 'Storage health check', pattern: 'checkStorageHealth' },
      { name: 'Periodic health checks', pattern: 'startPeriodicHealthChecks' }
    ];
    
    console.log('Monitoring middleware features:');
    checks.forEach(check => {
      const hasFeature = containsPattern(content, check.pattern);
      console.log(`   ${hasFeature ? '✅' : '❌'} ${check.name}: ${hasFeature}`);
    });
    
    return true;
  } else {
    console.log('❌ Monitoring middleware file not found');
    return false;
  }
}

async function testErrorHandling() {
  console.log('\n🔍 Testing Error Handling...\n');
  
  const errorHandlerFile = 'src/utils/errorHandler.ts';
  
  if (fileExists(errorHandlerFile)) {
    const content = readFile(errorHandlerFile);
    
    const checks = [
      { name: 'Custom error class', pattern: 'AppError.*extends.*Error' },
      { name: 'Error types enum', pattern: 'ErrorType.*=.*{' },
      { name: 'Error creators', pattern: 'createError.*=' },
      { name: 'Async handler', pattern: 'asyncHandler' },
      { name: 'Global error handlers', pattern: 'setupGlobalErrorHandlers' },
      { name: 'Uncaught exception handler', pattern: 'uncaughtException' },
      { name: 'Unhandled rejection handler', pattern: 'unhandledRejection' },
      { name: 'Database error handler', pattern: 'handleDatabaseError' }
    ];
    
    console.log('Error handling features:');
    checks.forEach(check => {
      const hasFeature = containsPattern(content, check.pattern);
      console.log(`   ${hasFeature ? '✅' : '❌'} ${check.name}: ${hasFeature}`);
    });
    
    return true;
  } else {
    console.log('❌ Error handler file not found');
    return false;
  }
}

async function testServerIntegration() {
  console.log('\n🔍 Testing Server Integration...\n');
  
  const serverFile = 'src/index.ts';
  
  if (fileExists(serverFile)) {
    const content = readFile(serverFile);
    
    const checks = [
      { name: 'Logger import', pattern: 'import.*logger' },
      { name: 'Monitoring middleware import', pattern: 'import.*monitoring' },
      { name: 'Error handler import', pattern: 'import.*errorHandler' },
      { name: 'Request timing middleware', pattern: 'requestTiming' },
      { name: 'Metrics collection middleware', pattern: 'collectMetrics' },
      { name: 'Health check endpoint', pattern: 'healthCheck' },
      { name: 'Error tracking middleware', pattern: 'errorTracking' },
      { name: 'Global error handlers setup', pattern: 'setupGlobalErrorHandlers' },
      { name: 'Periodic health checks start', pattern: 'startPeriodicHealthChecks' }
    ];
    
    console.log('Server integration features:');
    checks.forEach(check => {
      const hasFeature = containsPattern(content, check.pattern);
      console.log(`   ${hasFeature ? '✅' : '❌'} ${check.name}: ${hasFeature}`);
    });
    
    return true;
  } else {
    console.log('❌ Server file not found');
    return false;
  }
}

async function testMonitoringDashboard() {
  console.log('\n🔍 Testing Monitoring Dashboard...\n');
  
  const dashboardFile = 'monitor-production.js';
  
  if (fileExists(dashboardFile)) {
    const content = readFile(dashboardFile);
    
    const checks = [
      { name: 'Backend health check', pattern: 'checkBackendHealth' },
      { name: 'Edge function health check', pattern: 'checkEdgeFunctionHealth' },
      { name: 'Database health check', pattern: 'checkDatabaseHealth' },
      { name: 'Statistics calculation', pattern: 'calculateStats' },
      { name: 'Alert system', pattern: 'addAlert' },
      { name: 'Dashboard display', pattern: 'displayDashboard' },
      { name: 'Periodic monitoring', pattern: 'setInterval' },
      { name: 'Graceful shutdown', pattern: 'SIGINT|SIGTERM' }
    ];
    
    console.log('Monitoring dashboard features:');
    checks.forEach(check => {
      const hasFeature = containsPattern(content, check.pattern);
      console.log(`   ${hasFeature ? '✅' : '❌'} ${check.name}: ${hasFeature}`);
    });
    
    return true;
  } else {
    console.log('❌ Monitoring dashboard file not found');
    return false;
  }
}

async function testLogDirectorySetup() {
  console.log('\n🔍 Testing Log Directory Setup...\n');
  
  const logsDir = 'logs';
  const logsDirExists = fileExists(logsDir);
  
  console.log(`${logsDirExists ? '✅' : '📁'} logs/ directory: ${logsDirExists ? 'Exists' : 'Will be created automatically'}`);
  
  // Check if logs directory can be created
  if (!logsDirExists) {
    try {
      fs.mkdirSync(logsDir, { recursive: true });
      console.log('✅ Successfully created logs directory');
      
      // Create a test log file
      const testLogFile = path.join(logsDir, 'test.log');
      fs.writeFileSync(testLogFile, 'Test log entry\n');
      console.log('✅ Successfully created test log file');
      
      // Clean up test file
      fs.unlinkSync(testLogFile);
      console.log('✅ Test log file cleaned up');
      
      return true;
    } catch (error) {
      console.log(`❌ Failed to create logs directory: ${error.message}`);
      return false;
    }
  }
  
  return true;
}

async function testEnvironmentConfiguration() {
  console.log('\n🔍 Testing Environment Configuration...\n');
  
  const envFiles = [
    '.env.example',
    'environments/production.env.example',
    'environments/staging.env.example'
  ];
  
  let hasMonitoringConfig = false;
  
  for (const envFile of envFiles) {
    const exists = fileExists(envFile);
    console.log(`${exists ? '✅' : '❌'} ${envFile}: ${exists ? 'Found' : 'Missing'}`);
    
    if (exists) {
      const content = readFile(envFile);
      if (content) {
        // Check for monitoring-related environment variables
        const hasLogLevel = containsPattern(content, 'LOG_LEVEL');
        const hasLogFile = containsPattern(content, 'LOG_FILE');
        const hasSentry = containsPattern(content, 'SENTRY_DSN');
        const hasAnalytics = containsPattern(content, 'ANALYTICS_ENABLED');
        
        console.log(`   ${hasLogLevel ? '✅' : '❌'} LOG_LEVEL configured: ${hasLogLevel}`);
        console.log(`   ${hasLogFile ? '✅' : '❌'} LOG_FILE configured: ${hasLogFile}`);
        console.log(`   ${hasSentry ? '✅' : '❌'} SENTRY_DSN configured: ${hasSentry}`);
        console.log(`   ${hasAnalytics ? '✅' : '❌'} ANALYTICS_ENABLED configured: ${hasAnalytics}`);
        
        if (hasLogLevel || hasLogFile || hasSentry || hasAnalytics) {
          hasMonitoringConfig = true;
        }
      }
    }
  }
  
  return hasMonitoringConfig;
}

async function testPackageDependencies() {
  console.log('\n🔍 Testing Package Dependencies...\n');
  
  const packageJsonFile = 'package.json';
  
  if (fileExists(packageJsonFile)) {
    const content = readFile(packageJsonFile);
    const packageJson = JSON.parse(content);
    
    const requiredDeps = ['winston'];
    const optionalDeps = ['@sentry/node', 'newrelic'];
    
    console.log('Required dependencies:');
    requiredDeps.forEach(dep => {
      const hasInDeps = packageJson.dependencies && packageJson.dependencies[dep];
      const hasInDevDeps = packageJson.devDependencies && packageJson.devDependencies[dep];
      const exists = hasInDeps || hasInDevDeps;
      console.log(`   ${exists ? '✅' : '❌'} ${dep}: ${exists ? 'Installed' : 'Missing'}`);
    });
    
    console.log('\nOptional dependencies:');
    optionalDeps.forEach(dep => {
      const hasInDeps = packageJson.dependencies && packageJson.dependencies[dep];
      const hasInDevDeps = packageJson.devDependencies && packageJson.devDependencies[dep];
      const exists = hasInDeps || hasInDevDeps;
      console.log(`   ${exists ? '✅' : '📦'} ${dep}: ${exists ? 'Installed' : 'Not installed (optional)'}`);
    });
    
    return true;
  } else {
    console.log('❌ package.json not found');
    return false;
  }
}

// Main test runner
async function runMonitoringSetupTests() {
  console.log('🚀 Starting Production Monitoring Setup Test Suite');
  console.log('='.repeat(60));
  
  const results = {};
  
  try {
    results.loggingSetup = await testLoggingSetup();
    results.monitoringMiddleware = await testMonitoringMiddleware();
    results.errorHandling = await testErrorHandling();
    results.serverIntegration = await testServerIntegration();
    results.monitoringDashboard = await testMonitoringDashboard();
    results.logDirectorySetup = await testLogDirectorySetup();
    results.environmentConfiguration = await testEnvironmentConfiguration();
    results.packageDependencies = await testPackageDependencies();
    
    console.log('\n📊 Test Summary:');
    console.log('='.repeat(30));
    
    Object.entries(results).forEach(([testName, result]) => {
      const status = result ? '✅ Pass' : '❌ Fail';
      const formattedName = testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      console.log(`${formattedName}: ${status}`);
    });
    
    const overallSuccess = Object.values(results).every(result => result);
    
    console.log('\n' + '='.repeat(60));
    console.log(`${overallSuccess ? '✅' : '❌'} Production monitoring setup ${overallSuccess ? 'completed successfully' : 'completed with issues'}!`);
    
    if (overallSuccess) {
      console.log('\n🎉 Production monitoring is fully configured!');
      console.log('   - Winston logging with file and console transports');
      console.log('   - Comprehensive error handling with custom error types');
      console.log('   - Request timing and metrics collection middleware');
      console.log('   - Health check endpoints with detailed metrics');
      console.log('   - Production monitoring dashboard');
      console.log('   - Global error handlers for uncaught exceptions');
      console.log('   - Environment configuration for different stages');
      console.log('   - Automatic log directory creation');
      console.log('\n📋 Next steps:');
      console.log('   1. Start the backend server: npm run dev');
      console.log('   2. Run the monitoring dashboard: node monitor-production.js');
      console.log('   3. Check health endpoint: http://localhost:3000/health');
      console.log('   4. Monitor logs in the logs/ directory');
    } else {
      console.log('\n⚠️  Some monitoring components need attention. Please review the failed tests above.');
    }
    
  } catch (error) {
    console.log(`❌ Test suite failed: ${error.message}`);
  }
}

// Run the tests
runMonitoringSetupTests().catch(console.error);
