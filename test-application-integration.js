// Comprehensive application integration test
import fetch from 'node-fetch';

console.log('🧪 Doc Maker App - Application Integration Test');
console.log('='.repeat(70));
console.log(`Test started: ${new Date().toISOString()}\n`);

// Configuration
const config = {
  backend: {
    url: 'http://localhost:3000',
    endpoints: ['/health', '/api/test', '/api/v1']
  },
  frontend: {
    url: 'http://localhost:3001',
    endpoints: ['/frontend-health', '/']
  },
  supabase: {
    url: 'https://idvceughudfmupvoxgns.supabase.co',
    edgeFunctions: ['upload-template', 'create-report', 'document-processor'],
    restApi: '/rest/v1/'
  },
  timeout: 10000
};

// Test backend server
async function testBackendServer() {
  console.log('🔧 Testing Backend Server...\n');
  
  const results = {};
  
  for (const endpoint of config.backend.endpoints) {
    const url = `${config.backend.url}${endpoint}`;
    console.log(`Testing ${url}...`);
    
    const startTime = Date.now();
    
    try {
      const response = await fetch(url, {
        timeout: config.timeout,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      
      const responseTime = Date.now() - startTime;
      const contentType = response.headers.get('content-type');
      
      let data = null;
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }
      
      results[endpoint] = {
        status: response.status,
        statusText: response.statusText,
        responseTime,
        contentType,
        data,
        accessible: true,
        error: null
      };
      
      console.log(`  ✅ ${endpoint}: ${response.status} ${response.statusText} (${responseTime}ms)`);
      
      if (endpoint === '/health' && data && data.status === 'OK') {
        console.log(`     Service: ${data.service || 'Unknown'}`);
        console.log(`     Uptime: ${data.uptime ? Math.round(data.uptime) + 's' : 'Unknown'}`);
      }
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      results[endpoint] = {
        status: 0,
        responseTime,
        accessible: false,
        error: error.message,
        data: null
      };
      
      console.log(`  ❌ ${endpoint}: Error - ${error.message} (${responseTime}ms)`);
    }
  }
  
  return results;
}

// Test frontend server
async function testFrontendServer() {
  console.log('\n🌐 Testing Frontend Server...\n');
  
  const results = {};
  
  for (const endpoint of config.frontend.endpoints) {
    const url = `${config.frontend.url}${endpoint}`;
    console.log(`Testing ${url}...`);
    
    const startTime = Date.now();
    
    try {
      const response = await fetch(url, {
        timeout: config.timeout,
        headers: {
          'Accept': 'text/html,application/json',
          'User-Agent': 'Doc-Maker-App-Test/1.0'
        }
      });
      
      const responseTime = Date.now() - startTime;
      const contentType = response.headers.get('content-type');
      
      let data = null;
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        const text = await response.text();
        data = text.length > 200 ? `${text.substring(0, 200)}...` : text;
      }
      
      results[endpoint] = {
        status: response.status,
        statusText: response.statusText,
        responseTime,
        contentType,
        data,
        accessible: true,
        error: null
      };
      
      console.log(`  ✅ ${endpoint}: ${response.status} ${response.statusText} (${responseTime}ms)`);
      
      if (endpoint === '/frontend-health' && data && data.status === 'OK') {
        console.log(`     Service: ${data.service || 'Unknown'}`);
        console.log(`     Port: ${data.port || 'Unknown'}`);
      }
      
      if (endpoint === '/' && contentType && contentType.includes('text/html')) {
        console.log(`     Content: HTML page loaded successfully`);
      }
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      results[endpoint] = {
        status: 0,
        responseTime,
        accessible: false,
        error: error.message,
        data: null
      };
      
      console.log(`  ❌ ${endpoint}: Error - ${error.message} (${responseTime}ms)`);
    }
  }
  
  return results;
}

// Test Supabase services
async function testSupabaseServices() {
  console.log('\n☁️  Testing Supabase Services...\n');
  
  const results = {};
  
  // Test REST API
  console.log(`Testing ${config.supabase.url}${config.supabase.restApi}...`);
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${config.supabase.url}${config.supabase.restApi}`, {
      timeout: config.timeout,
      headers: {
        'apikey': 'test-key' // This will fail but shows connectivity
      }
    });
    
    const responseTime = Date.now() - startTime;
    
    results.restApi = {
      status: response.status,
      statusText: response.statusText,
      responseTime,
      accessible: true,
      error: null
    };
    
    console.log(`  ✅ REST API: ${response.status} ${response.statusText} (${responseTime}ms)`);
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    results.restApi = {
      status: 0,
      responseTime,
      accessible: false,
      error: error.message
    };
    
    console.log(`  ❌ REST API: Error - ${error.message} (${responseTime}ms)`);
  }
  
  // Test Edge Functions
  console.log('\nTesting Edge Functions...');
  results.edgeFunctions = {};
  
  for (const functionName of config.supabase.edgeFunctions) {
    const url = `${config.supabase.url}/functions/v1/${functionName}`;
    console.log(`Testing ${functionName}...`);
    
    const startTime = Date.now();
    
    try {
      const response = await fetch(url, {
        timeout: config.timeout,
        method: 'GET'
      });
      
      const responseTime = Date.now() - startTime;
      
      results.edgeFunctions[functionName] = {
        status: response.status,
        statusText: response.statusText,
        responseTime,
        accessible: true,
        error: null
      };
      
      console.log(`  ✅ ${functionName}: ${response.status} ${response.statusText} (${responseTime}ms)`);
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      results.edgeFunctions[functionName] = {
        status: 0,
        responseTime,
        accessible: false,
        error: error.message
      };
      
      console.log(`  ❌ ${functionName}: Error - ${error.message} (${responseTime}ms)`);
    }
  }
  
  return results;
}

// Generate integration report
function generateIntegrationReport(backendResults, frontendResults, supabaseResults) {
  console.log('\n📊 Application Integration Report');
  console.log('='.repeat(70));
  
  // Overall status
  const backendHealthy = backendResults['/health'] && backendResults['/health'].accessible;
  const frontendHealthy = frontendResults['/frontend-health'] && frontendResults['/frontend-health'].accessible;
  const supabaseHealthy = supabaseResults.restApi && supabaseResults.restApi.accessible;
  
  const overallStatus = backendHealthy && frontendHealthy && supabaseHealthy;
  
  console.log(`Overall Application Status: ${overallStatus ? '✅ Healthy' : '❌ Issues Detected'}`);
  console.log('');
  
  // Backend summary
  console.log('🔧 Backend Server:');
  console.log(`  Status: ${backendHealthy ? '✅ Online' : '❌ Offline'}`);
  console.log(`  URL: ${config.backend.url}`);
  
  if (backendResults['/health'] && backendResults['/health'].data) {
    const healthData = backendResults['/health'].data;
    console.log(`  Service: ${healthData.service || 'Unknown'}`);
    console.log(`  Uptime: ${healthData.uptime ? Math.round(healthData.uptime) + 's' : 'Unknown'}`);
  }
  
  console.log('');
  
  // Frontend summary
  console.log('🌐 Frontend Server:');
  console.log(`  Status: ${frontendHealthy ? '✅ Online' : '❌ Offline'}`);
  console.log(`  URL: ${config.frontend.url}`);
  
  if (frontendResults['/frontend-health'] && frontendResults['/frontend-health'].data) {
    const healthData = frontendResults['/frontend-health'].data;
    console.log(`  Service: ${healthData.service || 'Unknown'}`);
    console.log(`  Port: ${healthData.port || 'Unknown'}`);
  }
  
  console.log('');
  
  // Supabase summary
  console.log('☁️  Supabase Services:');
  console.log(`  REST API: ${supabaseHealthy ? '✅ Accessible' : '❌ Unreachable'}`);
  console.log(`  URL: ${config.supabase.url}`);
  
  console.log('  Edge Functions:');
  Object.entries(supabaseResults.edgeFunctions || {}).forEach(([name, result]) => {
    const status = result.accessible ? '✅' : '❌';
    const statusText = result.accessible ? `${result.status}` : 'Unreachable';
    console.log(`    ${status} ${name}: ${statusText} (${result.responseTime}ms)`);
  });
  
  console.log('');
  
  // Performance metrics
  console.log('📈 Performance Metrics:');
  
  const backendResponseTimes = Object.values(backendResults).map(r => r.responseTime).filter(t => t);
  const frontendResponseTimes = Object.values(frontendResults).map(r => r.responseTime).filter(t => t);
  const edgeFunctionResponseTimes = Object.values(supabaseResults.edgeFunctions || {}).map(r => r.responseTime).filter(t => t);
  
  if (backendResponseTimes.length > 0) {
    const avgBackend = backendResponseTimes.reduce((a, b) => a + b, 0) / backendResponseTimes.length;
    console.log(`  Backend Average Response Time: ${avgBackend.toFixed(0)}ms`);
  }
  
  if (frontendResponseTimes.length > 0) {
    const avgFrontend = frontendResponseTimes.reduce((a, b) => a + b, 0) / frontendResponseTimes.length;
    console.log(`  Frontend Average Response Time: ${avgFrontend.toFixed(0)}ms`);
  }
  
  if (edgeFunctionResponseTimes.length > 0) {
    const avgEdgeFunctions = edgeFunctionResponseTimes.reduce((a, b) => a + b, 0) / edgeFunctionResponseTimes.length;
    console.log(`  Edge Functions Average Response Time: ${avgEdgeFunctions.toFixed(0)}ms`);
  }
  
  console.log('');
  
  // Manual testing instructions
  console.log('🧪 Manual Testing Instructions:');
  console.log(`  1. Open ${config.frontend.url} in your browser`);
  console.log(`  2. Use the test interface to verify backend connectivity`);
  console.log(`  3. Test Supabase Edge Functions through the interface`);
  console.log(`  4. Check browser console for any JavaScript errors`);
  console.log(`  5. Verify all status indicators show green (online)`);
  
  console.log('');
  
  return {
    overallStatus,
    backend: backendResults,
    frontend: frontendResults,
    supabase: supabaseResults,
    healthy: {
      backend: backendHealthy,
      frontend: frontendHealthy,
      supabase: supabaseHealthy
    }
  };
}

// Main test function
async function runIntegrationTest() {
  try {
    // Test all services
    const backendResults = await testBackendServer();
    const frontendResults = await testFrontendServer();
    const supabaseResults = await testSupabaseServices();
    
    // Generate report
    const report = generateIntegrationReport(backendResults, frontendResults, supabaseResults);
    
    // Save report
    const fs = await import('fs');
    const logsDir = 'logs';
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    const reportData = {
      timestamp: new Date().toISOString(),
      ...report
    };
    
    fs.writeFileSync(
      `${logsDir}/integration-test-${Date.now()}.json`,
      JSON.stringify(reportData, null, 2)
    );
    
    console.log(`📄 Report saved to logs/integration-test-${Date.now()}.json`);
    
    return report.overallStatus;
    
  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    return false;
  }
}

// Run the test
runIntegrationTest()
  .then(success => {
    console.log('\n' + '='.repeat(70));
    console.log(`${success ? '✅' : '❌'} Integration test ${success ? 'completed successfully' : 'completed with issues'}!`);
    
    if (success) {
      console.log('\n🎉 Application is ready for manual testing!');
      console.log('   - Frontend: http://localhost:3001');
      console.log('   - Backend: http://localhost:3000');
      console.log('   - Both servers are running and accessible');
    }
    
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  });
