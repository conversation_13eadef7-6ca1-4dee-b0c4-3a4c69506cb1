# Get these from your Supabase project settings > API
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Production Example:
# SUPABASE_URL=https://your-project-id.supabase.co
# SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
# SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# Application Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=7d

# Stripe Configuration (for payment processing)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password_here
FROM_EMAIL=<EMAIL>

# File Storage Configuration
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=.docx,.xlsx,.pdf
UPLOAD_PATH=./uploads

# Document Processing Configuration
DOCX_TEMPLATE_PATH=./templates
PDF_OUTPUT_PATH=./generated
TEMP_DIR=./temp

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Security
BCRYPT_ROUNDS=12
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# OpenAI Configuration (if using AI features)
OPENAI_API_KEY=your_openai_api_key_here

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379

# Multi-tenant Configuration
DEFAULT_TENANT_PLAN=free
MAX_DOCUMENTS_PER_TENANT=100
MAX_STORAGE_PER_TENANT=1GB
