// Test script to verify server startup
import { spawn } from 'child_process';
import fetch from 'node-fetch';

console.log('🚀 Testing Server Startup...\n');

// Function to test server startup
async function testServerStartup() {
  return new Promise((resolve, reject) => {
    console.log('Starting server...');
    
    // Start the server process
    const serverProcess = spawn('node', ['dist/src/index.js'], {
      cwd: process.cwd(),
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });
    
    let serverOutput = '';
    let serverError = '';
    
    // Capture stdout
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      serverOutput += output;
      console.log('Server output:', output.trim());
    });
    
    // Capture stderr
    serverProcess.stderr.on('data', (data) => {
      const error = data.toString();
      serverError += error;
      console.error('Server error:', error.trim());
    });
    
    // Handle process exit
    serverProcess.on('close', (code) => {
      console.log(`Server process exited with code ${code}`);
      
      if (code === 0) {
        resolve({ success: true, output: serverOutput, error: serverError });
      } else {
        resolve({ success: false, output: serverOutput, error: serverError, exitCode: code });
      }
    });
    
    // Handle process error
    serverProcess.on('error', (error) => {
      console.error('Failed to start server process:', error);
      reject(error);
    });
    
    // Wait a bit for server to start, then test health endpoint
    setTimeout(async () => {
      try {
        console.log('Testing health endpoint...');
        const response = await fetch('http://localhost:3000/health', {
          timeout: 5000
        });
        
        if (response.ok) {
          const data = await response.json();
          console.log('✅ Health check successful:', data);
          
          // Kill the server process
          serverProcess.kill('SIGTERM');
          
          resolve({ 
            success: true, 
            output: serverOutput, 
            error: serverError,
            healthCheck: data 
          });
        } else {
          console.log('❌ Health check failed:', response.status, response.statusText);
          serverProcess.kill('SIGTERM');
          
          resolve({ 
            success: false, 
            output: serverOutput, 
            error: serverError,
            healthCheckError: `${response.status} ${response.statusText}`
          });
        }
      } catch (error) {
        console.log('❌ Health check error:', error.message);
        serverProcess.kill('SIGTERM');
        
        resolve({ 
          success: false, 
          output: serverOutput, 
          error: serverError,
          healthCheckError: error.message
        });
      }
    }, 3000); // Wait 3 seconds for server to start
  });
}

// Function to test simple server startup
async function testSimpleServerStartup() {
  return new Promise((resolve, reject) => {
    console.log('\n🔧 Testing Simple Server Startup...');
    
    // Start the simple server process
    const serverProcess = spawn('node', ['simple-server.js'], {
      cwd: process.cwd(),
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });
    
    let serverOutput = '';
    let serverError = '';
    
    // Capture stdout
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      serverOutput += output;
      console.log('Simple server output:', output.trim());
    });
    
    // Capture stderr
    serverProcess.stderr.on('data', (data) => {
      const error = data.toString();
      serverError += error;
      console.error('Simple server error:', error.trim());
    });
    
    // Handle process exit
    serverProcess.on('close', (code) => {
      console.log(`Simple server process exited with code ${code}`);
      
      if (code === 0) {
        resolve({ success: true, output: serverOutput, error: serverError });
      } else {
        resolve({ success: false, output: serverOutput, error: serverError, exitCode: code });
      }
    });
    
    // Handle process error
    serverProcess.on('error', (error) => {
      console.error('Failed to start simple server process:', error);
      reject(error);
    });
    
    // Wait a bit for server to start, then test health endpoint
    setTimeout(async () => {
      try {
        console.log('Testing simple server health endpoint...');
        const response = await fetch('http://localhost:3000/health', {
          timeout: 5000
        });
        
        if (response.ok) {
          const data = await response.json();
          console.log('✅ Simple server health check successful:', data);
          
          // Kill the server process
          serverProcess.kill('SIGTERM');
          
          resolve({ 
            success: true, 
            output: serverOutput, 
            error: serverError,
            healthCheck: data 
          });
        } else {
          console.log('❌ Simple server health check failed:', response.status, response.statusText);
          serverProcess.kill('SIGTERM');
          
          resolve({ 
            success: false, 
            output: serverOutput, 
            error: serverError,
            healthCheckError: `${response.status} ${response.statusText}`
          });
        }
      } catch (error) {
        console.log('❌ Simple server health check error:', error.message);
        serverProcess.kill('SIGTERM');
        
        resolve({ 
          success: false, 
          output: serverOutput, 
          error: serverError,
          healthCheckError: error.message
        });
      }
    }, 3000); // Wait 3 seconds for server to start
  });
}

// Main test function
async function runServerTests() {
  try {
    // Test main server
    console.log('='.repeat(60));
    console.log('Testing Main Server (dist/src/index.js)');
    console.log('='.repeat(60));
    
    const mainServerResult = await testServerStartup();
    
    console.log('\n📊 Main Server Test Results:');
    console.log('Success:', mainServerResult.success);
    console.log('Output:', mainServerResult.output || 'No output');
    console.log('Error:', mainServerResult.error || 'No errors');
    
    if (mainServerResult.healthCheck) {
      console.log('Health Check:', JSON.stringify(mainServerResult.healthCheck, null, 2));
    }
    
    if (mainServerResult.healthCheckError) {
      console.log('Health Check Error:', mainServerResult.healthCheckError);
    }
    
    // Test simple server
    console.log('\n' + '='.repeat(60));
    console.log('Testing Simple Server (simple-server.js)');
    console.log('='.repeat(60));
    
    const simpleServerResult = await testSimpleServerStartup();
    
    console.log('\n📊 Simple Server Test Results:');
    console.log('Success:', simpleServerResult.success);
    console.log('Output:', simpleServerResult.output || 'No output');
    console.log('Error:', simpleServerResult.error || 'No errors');
    
    if (simpleServerResult.healthCheck) {
      console.log('Health Check:', JSON.stringify(simpleServerResult.healthCheck, null, 2));
    }
    
    if (simpleServerResult.healthCheckError) {
      console.log('Health Check Error:', simpleServerResult.healthCheckError);
    }
    
    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📋 Test Summary');
    console.log('='.repeat(60));
    
    console.log(`Main Server: ${mainServerResult.success ? '✅ Working' : '❌ Failed'}`);
    console.log(`Simple Server: ${simpleServerResult.success ? '✅ Working' : '❌ Failed'}`);
    
    if (mainServerResult.success || simpleServerResult.success) {
      console.log('\n✅ At least one server configuration is working!');
      console.log('You can proceed with manual testing using the working server.');
    } else {
      console.log('\n❌ Both server configurations failed to start.');
      console.log('Please check the error messages above for troubleshooting.');
    }
    
    return mainServerResult.success || simpleServerResult.success;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

// Run the tests
runServerTests()
  .then(success => {
    console.log(`\n🏁 Server startup test ${success ? 'completed successfully' : 'failed'}!`);
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  });
