{"name": "doc-maker-app", "version": "1.0.0", "description": "Multi-tenant SaaS application for dynamic report generation with DOCX to PDF conversion", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx --no-check watch src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "type-check": "tsc --noEmit", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:generate-types": "supabase gen types typescript --local > src/types/supabase.ts", "db:migrate": "supabase migration up", "db:seed": "supabase seed", "functions:serve": "supabase functions serve", "functions:deploy": "supabase functions deploy"}, "keywords": ["supabase", "typescript", "saas", "multi-tenant", "document-processing", "pdf-conversion", "docx", "reports"], "author": "Your Name", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "docxtemplater": "^3.44.0", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pdf-lib": "^1.17.1", "pizzip": "^3.1.6", "puppeteer": "^21.6.1", "rate-limiter-flexible": "^4.0.1", "sharp": "^0.33.1", "stripe": "^14.12.0", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^30.0.5", "jsdom": "^26.1.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsc-alias": "^1.8.8", "tsx": "^4.20.3", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}