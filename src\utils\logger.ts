/**
 * Production-ready logging utility with <PERSON>
 */

import winston from 'winston';
import path from 'path';

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define log colors
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Add colors to winston
winston.addColors(logColors);

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

// Define transports
const transports = [
  // Console transport for development
  new winston.transports.Console({
    format: logFormat,
  }),
  
  // File transport for errors
  new winston.transports.File({
    filename: path.join(process.cwd(), 'logs', 'error.log'),
    level: 'error',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: path.join(process.cwd(), 'logs', 'combined.log'),
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
  }),
];

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels: logLevels,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports,
  exitOnError: false,
});

// Create logs directory if it doesn't exist
import fs from 'fs';
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Enhanced logging methods with context
export const log = {
  error: (message: string, meta?: any) => {
    logger.error(message, { ...meta, timestamp: new Date().toISOString() });
  },
  
  warn: (message: string, meta?: any) => {
    logger.warn(message, { ...meta, timestamp: new Date().toISOString() });
  },
  
  info: (message: string, meta?: any) => {
    logger.info(message, { ...meta, timestamp: new Date().toISOString() });
  },
  
  http: (message: string, meta?: any) => {
    logger.http(message, { ...meta, timestamp: new Date().toISOString() });
  },
  
  debug: (message: string, meta?: any) => {
    logger.debug(message, { ...meta, timestamp: new Date().toISOString() });
  },
  
  // Specialized logging methods
  request: (req: any, res: any, responseTime?: number) => {
    logger.http('HTTP Request', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: responseTime ? `${responseTime}ms` : undefined,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      timestamp: new Date().toISOString(),
    });
  },
  
  database: (operation: string, table: string, meta?: any) => {
    logger.info('Database Operation', {
      operation,
      table,
      ...meta,
      timestamp: new Date().toISOString(),
    });
  },
  
  auth: (event: string, userId?: string, meta?: any) => {
    logger.info('Authentication Event', {
      event,
      userId,
      ...meta,
      timestamp: new Date().toISOString(),
    });
  },
  
  security: (event: string, meta?: any) => {
    logger.warn('Security Event', {
      event,
      ...meta,
      timestamp: new Date().toISOString(),
    });
  },
  
  performance: (operation: string, duration: number, meta?: any) => {
    logger.info('Performance Metric', {
      operation,
      duration: `${duration}ms`,
      ...meta,
      timestamp: new Date().toISOString(),
    });
  },
  
  business: (event: string, meta?: any) => {
    logger.info('Business Event', {
      event,
      ...meta,
      timestamp: new Date().toISOString(),
    });
  },
};

export default logger;
