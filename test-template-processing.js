// Test script for Template Processing Logic
import { createClient } from '@supabase/supabase-js';

// Configuration from .env
const SUPABASE_URL = 'https://idvceughudfmupvoxgns.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlkdmNldWdodWRmbXVwdm94Z25zIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzcwODAxMSwiZXhwIjoyMDY5Mjg0MDExfQ.SItAasQ7LG3TGVEnxgY-8ksS-lP7Op3Daag_qaiZ-jM';

// Initialize Supabase client
const supabaseService = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testDatabaseSchema() {
  console.log('\n🔍 Testing Database Schema...\n');
  
  try {
    // Test document_templates table structure
    const { data: templates, error: templatesError } = await supabaseService
      .from('document_templates')
      .select('*')
      .limit(1);
    
    if (templatesError) {
      console.log(`❌ Templates table access failed: ${templatesError.message}`);
      return false;
    }
    
    console.log('✅ document_templates table accessible');
    
    // Test documents table structure
    const { data: documents, error: documentsError } = await supabaseService
      .from('documents')
      .select('*')
      .limit(1);
    
    if (documentsError) {
      console.log(`❌ Documents table access failed: ${documentsError.message}`);
      return false;
    }
    
    console.log('✅ documents table accessible');
    
    // Test storage buckets
    const { data: buckets, error: bucketsError } = await supabaseService.storage.listBuckets();
    
    if (bucketsError) {
      console.log(`❌ Storage buckets access failed: ${bucketsError.message}`);
      return false;
    }
    
    console.log('✅ Storage buckets accessible');
    console.log(`   Available buckets: ${buckets.map(b => b.name).join(', ')}`);
    
    return true;
    
  } catch (error) {
    console.log(`❌ Database schema test error: ${error.message}`);
    return false;
  }
}

async function testTemplateCreation() {
  console.log('\n🔍 Testing Template Creation...\n');
  
  try {
    // Create a test tenant first
    const { data: tenant, error: tenantError } = await supabaseService
      .from('tenants')
      .insert({
        name: 'Template Test Company',
        slug: 'template-test-' + Date.now(),
        plan: 'free',
        settings: {}
      })
      .select()
      .single();
    
    if (tenantError) {
      throw new Error(`Failed to create test tenant: ${tenantError.message}`);
    }
    
    console.log(`✅ Test tenant created: ${tenant.id}`);
    
    // Create a test user
    const testEmail = `templatetest.${Date.now()}@gmail.com`;
    const { data: authData, error: authError } = await supabaseService.auth.admin.createUser({
      email: testEmail,
      password: 'TestPassword123!',
      email_confirm: true,
      user_metadata: {
        tenant_id: tenant.id,
        full_name: 'Template Test User'
      }
    });
    
    if (authError) {
      throw new Error(`Failed to create test user: ${authError.message}`);
    }
    
    console.log(`✅ Test user created: ${authData.user.id}`);
    
    // Create user profile
    const { error: profileError } = await supabaseService
      .from('user_profiles')
      .insert({
        id: authData.user.id,
        tenant_id: tenant.id,
        email: testEmail,
        full_name: 'Template Test User',
        role: 'user',
        is_active: true,
        preferences: {}
      });
    
    if (profileError) {
      throw new Error(`Failed to create user profile: ${profileError.message}`);
    }
    
    console.log(`✅ User profile created`);
    
    // Create a test template record
    const templateId = crypto.randomUUID();
    const { data: template, error: templateError } = await supabaseService
      .from('document_templates')
      .insert({
        id: templateId,
        tenant_id: tenant.id,
        name: 'Test Invoice Template',
        description: 'A test template for invoice generation',
        template_type: 'invoice',
        schema: ['company_name', 'client_name', 'amount', 'date', 'invoice_number'],
        storage_path: `templates/${tenant.id}/${templateId}.docx`,
        created_by: authData.user.id,
        is_active: true
      })
      .select()
      .single();
    
    if (templateError) {
      throw new Error(`Failed to create template: ${templateError.message}`);
    }
    
    console.log(`✅ Template created successfully`);
    console.log(`   Template ID: ${template.id}`);
    console.log(`   Template Name: ${template.name}`);
    console.log(`   Schema: ${JSON.stringify(template.schema)}`);
    
    return {
      tenant,
      user: authData.user,
      template
    };
    
  } catch (error) {
    console.log(`❌ Template creation failed: ${error.message}`);
    throw error;
  }
}

async function testDocumentCreation(testData) {
  console.log('\n🔍 Testing Document Creation...\n');
  
  try {
    // Create a test document record
    const documentId = crypto.randomUUID();
    const { data: document, error: documentError } = await supabaseService
      .from('documents')
      .insert({
        id: documentId,
        tenant_id: testData.tenant.id,
        template_id: testData.template.id,
        name: 'Test Invoice Document',
        status: 'draft',
        input_data: {
          company_name: 'Test Company Inc.',
          client_name: 'John Doe',
          amount: '$1,500.00',
          date: '2024-01-27',
          invoice_number: 'INV-001'
        },
        created_by: testData.user.id
      })
      .select()
      .single();
    
    if (documentError) {
      throw new Error(`Failed to create document: ${documentError.message}`);
    }
    
    console.log(`✅ Document created successfully`);
    console.log(`   Document ID: ${document.id}`);
    console.log(`   Document Name: ${document.name}`);
    console.log(`   Status: ${document.status}`);
    console.log(`   Input Data: ${JSON.stringify(document.input_data, null, 2)}`);
    
    return document;
    
  } catch (error) {
    console.log(`❌ Document creation failed: ${error.message}`);
    throw error;
  }
}

async function testStorageOperations(testData) {
  console.log('\n🔍 Testing Storage Operations...\n');
  
  try {
    // Test creating a mock template file in storage
    const mockDocxContent = `
      Mock DOCX content with placeholders:
      Company: {{company_name}}
      Client: {{client_name}}
      Amount: {{amount}}
      Date: {{date}}
      Invoice #: {{invoice_number}}
    `;
    
    const storagePath = testData.template.storage_path;
    
    // Upload mock file
    const { error: uploadError } = await supabaseService.storage
      .from('templates')
      .upload(storagePath, Buffer.from(mockDocxContent, 'utf8'), {
        contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        upsert: true
      });
    
    if (uploadError) {
      throw new Error(`Failed to upload mock template: ${uploadError.message}`);
    }
    
    console.log(`✅ Mock template uploaded to storage`);
    console.log(`   Storage path: ${storagePath}`);
    
    // Test retrieving the file
    const { data: fileData, error: downloadError } = await supabaseService.storage
      .from('templates')
      .download(storagePath);
    
    if (downloadError) {
      throw new Error(`Failed to download template: ${downloadError.message}`);
    }
    
    console.log(`✅ Template downloaded successfully`);
    console.log(`   File size: ${fileData.size} bytes`);
    
    // Test listing files in tenant folder
    const tenantFolder = `${testData.tenant.id}/`;
    const { data: files, error: listError } = await supabaseService.storage
      .from('templates')
      .list(tenantFolder);
    
    if (listError) {
      throw new Error(`Failed to list tenant files: ${listError.message}`);
    }
    
    console.log(`✅ Found ${files.length} files in tenant folder`);
    
    return true;
    
  } catch (error) {
    console.log(`❌ Storage operations failed: ${error.message}`);
    return false;
  }
}

async function testMultiTenantIsolation(testData) {
  console.log('\n🔍 Testing Multi-tenant Data Isolation...\n');
  
  try {
    // Create another tenant
    const { data: otherTenant, error: otherTenantError } = await supabaseService
      .from('tenants')
      .insert({
        name: 'Other Test Company',
        slug: 'other-test-' + Date.now(),
        plan: 'free',
        settings: {}
      })
      .select()
      .single();
    
    if (otherTenantError) {
      throw new Error(`Failed to create other tenant: ${otherTenantError.message}`);
    }
    
    console.log(`✅ Other tenant created: ${otherTenant.id}`);
    
    // Try to access templates from the other tenant (should return empty)
    const { data: otherTemplates, error: otherTemplatesError } = await supabaseService
      .from('document_templates')
      .select('*')
      .eq('tenant_id', otherTenant.id);
    
    if (otherTemplatesError) {
      throw new Error(`Failed to query other tenant templates: ${otherTemplatesError.message}`);
    }
    
    console.log(`✅ Other tenant has ${otherTemplates.length} templates (expected: 0)`);
    
    // Verify our tenant still has its template
    const { data: ourTemplates, error: ourTemplatesError } = await supabaseService
      .from('document_templates')
      .select('*')
      .eq('tenant_id', testData.tenant.id);
    
    if (ourTemplatesError) {
      throw new Error(`Failed to query our tenant templates: ${ourTemplatesError.message}`);
    }
    
    console.log(`✅ Our tenant has ${ourTemplates.length} templates (expected: 1)`);
    
    // Clean up other tenant
    await supabaseService
      .from('tenants')
      .delete()
      .eq('id', otherTenant.id);
    
    console.log(`✅ Multi-tenant isolation working correctly`);
    
    return true;
    
  } catch (error) {
    console.log(`❌ Multi-tenant isolation test failed: ${error.message}`);
    return false;
  }
}

async function cleanup(testData) {
  console.log('\n🧹 Cleaning up test data...\n');
  
  try {
    if (testData) {
      // Delete documents
      await supabaseService
        .from('documents')
        .delete()
        .eq('tenant_id', testData.tenant.id);
      
      // Delete templates
      await supabaseService
        .from('document_templates')
        .delete()
        .eq('tenant_id', testData.tenant.id);
      
      // Delete storage files
      const tenantFolder = `${testData.tenant.id}/`;
      const { data: files } = await supabaseService.storage
        .from('templates')
        .list(tenantFolder);
      
      if (files && files.length > 0) {
        const filePaths = files.map(file => `${tenantFolder}${file.name}`);
        await supabaseService.storage
          .from('templates')
          .remove(filePaths);
      }
      
      // Delete user profile
      await supabaseService
        .from('user_profiles')
        .delete()
        .eq('id', testData.user.id);
      
      // Delete user from auth
      await supabaseService.auth.admin.deleteUser(testData.user.id);
      
      // Delete tenant
      await supabaseService
        .from('tenants')
        .delete()
        .eq('id', testData.tenant.id);
    }
    
    console.log('✅ Cleanup completed');
  } catch (error) {
    console.log(`⚠️ Cleanup error: ${error.message}`);
  }
}

// Main test runner
async function runTemplateProcessingTests() {
  console.log('🚀 Starting Template Processing Test Suite');
  console.log('='.repeat(50));
  
  let testData = null;
  
  try {
    // Test database schema
    const schemaOk = await testDatabaseSchema();
    if (!schemaOk) return;
    
    // Test template creation
    testData = await testTemplateCreation();
    
    // Test document creation
    const document = await testDocumentCreation(testData);
    
    // Test storage operations
    await testStorageOperations(testData);
    
    // Test multi-tenant isolation
    await testMultiTenantIsolation(testData);
    
  } finally {
    // Always cleanup
    await cleanup(testData);
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ Template processing test suite completed!');
}

// Run the tests
runTemplateProcessingTests().catch(console.error);
