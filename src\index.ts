import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
// import { createClient } from '@supabase/supabase-js';
import reportRoutes from './routes/reports.js';
import { log } from './utils/logger';
import {
  requestTiming,
  collectMetrics,
  healthCheck,
  errorTracking,
  startPeriodicHealthChecks
} from './middleware/monitoring';
import { setupGlobalErrorHandlers } from './utils/errorHandler';

// Load environment variables
dotenv.config();

// Setup global error handlers
setupGlobalErrorHandlers();

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize Supabase client (for potential future use)
// const supabaseUrl = process.env.SUPABASE_URL!;
// const supabaseKey = process.env.SUPABASE_ANON_KEY!;
// const supabase = createClient(supabaseUrl, supabaseKey);

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Monitoring middleware
app.use(requestTiming);
app.use(collectMetrics);

// Health check endpoint with comprehensive monitoring
app.get('/health', healthCheck);

// API routes
app.get('/api/v1', (_req, res) => {
  res.json({
    message: 'Doc Maker App API v1',
    version: '1.0.0',
  });
});

// Mount report routes
app.use('/api', reportRoutes);

// Error handling middleware
app.use(errorTracking);

// 404 handler
app.use('*', (_req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found',
  });
});

// Start server
app.listen(PORT, () => {
  log.info('Server started successfully', {
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
    nodeVersion: process.version,
    pid: process.pid,
  });

  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API: http://localhost:${PORT}/api/v1`);

  // Start periodic health checks
  startPeriodicHealthChecks();
});

export default app;
