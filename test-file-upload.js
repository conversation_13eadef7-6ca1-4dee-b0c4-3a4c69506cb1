// Test script for File Upload and Template Processing
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import https from 'https';

// Configuration from .env
const SUPABASE_URL = 'https://idvceughudfmupvoxgns.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlkdmNldWdodWRmbXVwdm94Z25zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MDgwMTEsImV4cCI6MjA2OTI4NDAxMX0.kMH7OCjSTW3mSml5QI45_-6NOnW1vXyCLZPHV6_pmmg';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlkdmNldWdodWRmbXVwdm94Z25zIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzcwODAxMSwiZXhwIjoyMDY5Mjg0MDExfQ.SItAasQ7LG3TGVEnxgY-8ksS-lP7Op3Daag_qaiZ-jM';

const FUNCTIONS_BASE_URL = `${SUPABASE_URL}/functions/v1`;

// Initialize Supabase clients
const supabaseService = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Create a simple DOCX file for testing
function createTestDocxFile() {
  // This is a minimal DOCX file structure with placeholders
  const docxContent = `
    <w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
      <w:body>
        <w:p>
          <w:r>
            <w:t>Hello {{name}}, welcome to {{company}}!</w:t>
          </w:r>
        </w:p>
        <w:p>
          <w:r>
            <w:t>Your email is {{email}} and today's date is {{date}}.</w:t>
          </w:r>
        </w:p>
      </w:body>
    </w:document>
  `;
  
  // For testing purposes, we'll create a simple text file that mimics DOCX structure
  // In a real scenario, you'd use a proper DOCX library
  const testContent = `Test DOCX Content with placeholders: {{name}}, {{company}}, {{email}}, {{date}}`;
  
  return Buffer.from(testContent, 'utf8');
}

// Helper function to make HTTP requests with form data
function makeFormDataRequest(url, formData, headers = {}) {
  return new Promise((resolve, reject) => {
    const boundary = '----formdata-boundary-' + Math.random().toString(36);
    
    let body = '';
    
    // Add form fields
    for (const [key, value] of Object.entries(formData)) {
      if (key === 'file') {
        // Handle file field
        body += `--${boundary}\r\n`;
        body += `Content-Disposition: form-data; name="file"; filename="test-template.docx"\r\n`;
        body += `Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document\r\n\r\n`;
        body += value.toString('binary');
        body += '\r\n';
      } else {
        // Handle text fields
        body += `--${boundary}\r\n`;
        body += `Content-Disposition: form-data; name="${key}"\r\n\r\n`;
        body += value;
        body += '\r\n';
      }
    }
    
    body += `--${boundary}--\r\n`;
    
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': `multipart/form-data; boundary=${boundary}`,
        'Content-Length': Buffer.byteLength(body, 'binary'),
        ...headers
      }
    };
    
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(body, 'binary');
    req.end();
  });
}

async function setupTestEnvironment() {
  console.log('\n🔧 Setting up test environment...\n');
  
  try {
    // Create a test tenant
    const { data: tenant, error: tenantError } = await supabaseService
      .from('tenants')
      .insert({
        name: 'File Upload Test Company',
        slug: 'file-upload-test-' + Date.now(),
        plan: 'free',
        settings: {}
      })
      .select()
      .single();
    
    if (tenantError) {
      throw new Error(`Failed to create test tenant: ${tenantError.message}`);
    }
    
    console.log(`✅ Test tenant created: ${tenant.id}`);
    
    // Create a test user
    const testEmail = `filetest.${Date.now()}@gmail.com`;
    const { data: authData, error: authError } = await supabaseService.auth.admin.createUser({
      email: testEmail,
      password: 'TestPassword123!',
      email_confirm: true,
      user_metadata: {
        tenant_id: tenant.id,
        full_name: 'File Test User'
      }
    });
    
    if (authError) {
      throw new Error(`Failed to create test user: ${authError.message}`);
    }
    
    console.log(`✅ Test user created: ${authData.user.id}`);
    
    // Create user profile
    const { error: profileError } = await supabaseService
      .from('user_profiles')
      .insert({
        id: authData.user.id,
        tenant_id: tenant.id,
        email: testEmail,
        full_name: 'File Test User',
        role: 'user',
        is_active: true,
        preferences: {}
      });
    
    if (profileError) {
      throw new Error(`Failed to create user profile: ${profileError.message}`);
    }
    
    console.log(`✅ User profile created`);
    
    // Generate access token
    const { data: sessionData, error: sessionError } = await supabaseService.auth.admin.generateLink({
      type: 'magiclink',
      email: testEmail
    });
    
    if (sessionError) {
      throw new Error(`Failed to generate session: ${sessionError.message}`);
    }
    
    return {
      tenant,
      user: authData.user,
      accessToken: sessionData.properties?.access_token || 'mock-token-for-testing'
    };
    
  } catch (error) {
    console.log(`❌ Setup failed: ${error.message}`);
    throw error;
  }
}

async function testFileUpload(testEnv) {
  console.log('\n🔍 Testing File Upload...\n');
  
  try {
    const url = `${FUNCTIONS_BASE_URL}/upload-template`;
    
    // Create test DOCX file
    const testFile = createTestDocxFile();
    
    const formData = {
      file: testFile,
      name: 'Test Template Document',
      templateType: 'invoice'
    };
    
    const headers = {
      'Authorization': `Bearer ${testEnv.accessToken}`
    };
    
    console.log('Uploading test template...');
    const response = await makeFormDataRequest(url, formData, headers);
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ File upload successful');
      return response.data.data;
    } else {
      console.log('⚠️ File upload returned non-success status');
      return null;
    }
    
  } catch (error) {
    console.log(`❌ File upload test failed: ${error.message}`);
    return null;
  }
}

async function testTemplateRetrieval(testEnv) {
  console.log('\n🔍 Testing Template Retrieval...\n');
  
  try {
    // Test retrieving templates for the tenant
    const { data: templates, error } = await supabaseService
      .from('document_templates')
      .select('*')
      .eq('tenant_id', testEnv.tenant.id);
    
    if (error) {
      console.log(`❌ Template retrieval failed: ${error.message}`);
      return false;
    }
    
    console.log(`✅ Found ${templates.length} templates for tenant`);
    
    if (templates.length > 0) {
      console.log('Template details:');
      templates.forEach(template => {
        console.log(`  - ID: ${template.id}`);
        console.log(`  - Name: ${template.name}`);
        console.log(`  - Type: ${template.template_type || 'N/A'}`);
        console.log(`  - Schema: ${JSON.stringify(template.schema)}`);
        console.log(`  - Storage Path: ${template.storage_path}`);
        console.log('');
      });
    }
    
    return true;
    
  } catch (error) {
    console.log(`❌ Template retrieval error: ${error.message}`);
    return false;
  }
}

async function testStorageBucket() {
  console.log('\n🔍 Testing Storage Bucket Access...\n');
  
  try {
    // List files in templates bucket
    const { data: files, error } = await supabaseService.storage
      .from('templates')
      .list('', {
        limit: 10
      });
    
    if (error) {
      console.log(`❌ Storage bucket access failed: ${error.message}`);
      return false;
    }
    
    console.log(`✅ Storage bucket accessible, found ${files.length} items`);
    
    if (files.length > 0) {
      console.log('Storage contents:');
      files.forEach(file => {
        console.log(`  - ${file.name} (${file.metadata?.size || 'unknown size'})`);
      });
    }
    
    return true;
    
  } catch (error) {
    console.log(`❌ Storage bucket test error: ${error.message}`);
    return false;
  }
}

async function cleanup(testEnv) {
  console.log('\n🧹 Cleaning up test data...\n');
  
  try {
    if (testEnv) {
      // Delete templates
      await supabaseService
        .from('document_templates')
        .delete()
        .eq('tenant_id', testEnv.tenant.id);
      
      // Delete user profile
      await supabaseService
        .from('user_profiles')
        .delete()
        .eq('id', testEnv.user.id);
      
      // Delete user from auth
      await supabaseService.auth.admin.deleteUser(testEnv.user.id);
      
      // Delete tenant
      await supabaseService
        .from('tenants')
        .delete()
        .eq('id', testEnv.tenant.id);
      
      // Clean up storage files
      const { data: files } = await supabaseService.storage
        .from('templates')
        .list(`${testEnv.tenant.id}/`);
      
      if (files && files.length > 0) {
        const filePaths = files.map(file => `${testEnv.tenant.id}/${file.name}`);
        await supabaseService.storage
          .from('templates')
          .remove(filePaths);
      }
    }
    
    console.log('✅ Cleanup completed');
  } catch (error) {
    console.log(`⚠️ Cleanup error: ${error.message}`);
  }
}

// Main test runner
async function runFileUploadTests() {
  console.log('🚀 Starting File Upload and Template Processing Test Suite');
  console.log('='.repeat(60));
  
  let testEnv = null;
  
  try {
    // Setup test environment
    testEnv = await setupTestEnvironment();
    
    // Test storage bucket access
    await testStorageBucket();
    
    // Test file upload
    const uploadResult = await testFileUpload(testEnv);
    
    // Test template retrieval
    await testTemplateRetrieval(testEnv);
    
  } finally {
    // Always cleanup
    await cleanup(testEnv);
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('✅ File upload test suite completed!');
}

// Run the tests
runFileUploadTests().catch(console.error);
