// Test script for Frontend Components Integration
import fs from 'fs';
import path from 'path';

// Helper function to check if file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Helper function to read file content
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    return null;
  }
}

// Helper function to check if content contains specific patterns
function containsPattern(content, pattern) {
  return new RegExp(pattern, 'i').test(content);
}

async function testComponentStructure() {
  console.log('\n🔍 Testing Component Structure...\n');
  
  const components = [
    'components/TemplateForm.tsx',
    'components/DynamicField.tsx',
    'components/ui/LoadingSpinner.tsx',
    'components/ui/ErrorMessage.tsx'
  ];
  
  let allComponentsExist = true;
  
  for (const component of components) {
    const exists = fileExists(component);
    console.log(`${exists ? '✅' : '❌'} ${component}: ${exists ? 'Found' : 'Missing'}`);
    
    if (!exists) {
      allComponentsExist = false;
    }
  }
  
  return allComponentsExist;
}

async function testTypeDefinitions() {
  console.log('\n🔍 Testing Type Definitions...\n');
  
  const typeFiles = [
    'types/template.ts',
    'types/common.ts'
  ];
  
  let allTypesExist = true;
  
  for (const typeFile of typeFiles) {
    const exists = fileExists(typeFile);
    console.log(`${exists ? '✅' : '❌'} ${typeFile}: ${exists ? 'Found' : 'Missing'}`);
    
    if (exists) {
      const content = readFile(typeFile);
      if (content) {
        // Check for key interfaces
        const hasInterfaces = containsPattern(content, 'interface|type');
        console.log(`   ${hasInterfaces ? '✅' : '❌'} Contains type definitions: ${hasInterfaces}`);
      }
    } else {
      allTypesExist = false;
    }
  }
  
  return allTypesExist;
}

async function testServiceIntegration() {
  console.log('\n🔍 Testing Service Integration...\n');
  
  const serviceFiles = [
    'services/templateService.ts',
    'services/supabaseClient.ts'
  ];
  
  let allServicesExist = true;
  
  for (const serviceFile of serviceFiles) {
    const exists = fileExists(serviceFile);
    console.log(`${exists ? '✅' : '❌'} ${serviceFile}: ${exists ? 'Found' : 'Missing'}`);
    
    if (exists) {
      const content = readFile(serviceFile);
      if (content) {
        // Check for Supabase integration
        const hasSupabase = containsPattern(content, '@supabase/supabase-js|createClient');
        console.log(`   ${hasSupabase ? '✅' : '❌'} Supabase integration: ${hasSupabase}`);
        
        // Check for API functions
        const hasApiFunctions = containsPattern(content, 'export.*function|export.*const.*=');
        console.log(`   ${hasApiFunctions ? '✅' : '❌'} Exported functions: ${hasApiFunctions}`);
      }
    } else {
      allServicesExist = false;
    }
  }
  
  return allServicesExist;
}

async function testUtilityFunctions() {
  console.log('\n🔍 Testing Utility Functions...\n');
  
  const utilFiles = [
    'utils/fieldUtils.ts',
    'utils/validation.ts',
    'utils/storage.ts'
  ];
  
  let allUtilsExist = true;
  
  for (const utilFile of utilFiles) {
    const exists = fileExists(utilFile);
    console.log(`${exists ? '✅' : '❌'} ${utilFile}: ${exists ? 'Found' : 'Missing'}`);
    
    if (exists) {
      const content = readFile(utilFile);
      if (content) {
        // Check for exported functions
        const hasExports = containsPattern(content, 'export');
        console.log(`   ${hasExports ? '✅' : '❌'} Has exports: ${hasExports}`);
      }
    } else {
      allUtilsExist = false;
    }
  }
  
  return allUtilsExist;
}

async function testComponentIntegration() {
  console.log('\n🔍 Testing Component Integration...\n');
  
  // Test TemplateForm component
  const templateFormPath = 'components/TemplateForm.tsx';
  if (fileExists(templateFormPath)) {
    const content = readFile(templateFormPath);
    
    // Check for key integrations
    const checks = [
      { name: 'React imports', pattern: 'import.*React' },
      { name: 'Supabase service import', pattern: 'templateService|supabaseClient' },
      { name: 'DynamicField usage', pattern: 'DynamicField' },
      { name: 'State management', pattern: 'useState|useEffect' },
      { name: 'Form handling', pattern: 'onSubmit|handleSubmit' },
      { name: 'Error handling', pattern: 'error|Error' },
      { name: 'Loading states', pattern: 'loading|Loading' }
    ];
    
    console.log('TemplateForm component analysis:');
    checks.forEach(check => {
      const hasFeature = containsPattern(content, check.pattern);
      console.log(`   ${hasFeature ? '✅' : '❌'} ${check.name}: ${hasFeature}`);
    });
  }
  
  // Test DynamicField component
  const dynamicFieldPath = 'components/DynamicField.tsx';
  if (fileExists(dynamicFieldPath)) {
    const content = readFile(dynamicFieldPath);
    
    const checks = [
      { name: 'React imports', pattern: 'import.*React' },
      { name: 'TypeScript props', pattern: 'DynamicFieldProps' },
      { name: 'Field type handling', pattern: 'type.*=.*text|email|number' },
      { name: 'Accessibility', pattern: 'aria-|role=|htmlFor' },
      { name: 'Error display', pattern: 'error.*&&|error.*?' }
    ];
    
    console.log('\nDynamicField component analysis:');
    checks.forEach(check => {
      const hasFeature = containsPattern(content, check.pattern);
      console.log(`   ${hasFeature ? '✅' : '❌'} ${check.name}: ${hasFeature}`);
    });
  }
  
  return true;
}

async function testTestFiles() {
  console.log('\n🔍 Testing Test Files...\n');
  
  const testFiles = [
    '__tests__/components/TemplateForm.test.tsx',
    '__tests__/components/DynamicField.test.tsx'
  ];
  
  let hasTests = false;
  
  for (const testFile of testFiles) {
    const exists = fileExists(testFile);
    console.log(`${exists ? '✅' : '❌'} ${testFile}: ${exists ? 'Found' : 'Missing'}`);
    
    if (exists) {
      hasTests = true;
      const content = readFile(testFile);
      if (content) {
        // Check for test structure
        const hasDescribe = containsPattern(content, 'describe\\(');
        const hasTest = containsPattern(content, 'test\\(|it\\(');
        const hasRender = containsPattern(content, 'render\\(');
        
        console.log(`   ${hasDescribe ? '✅' : '❌'} Has test suites: ${hasDescribe}`);
        console.log(`   ${hasTest ? '✅' : '❌'} Has test cases: ${hasTest}`);
        console.log(`   ${hasRender ? '✅' : '❌'} Has component rendering: ${hasRender}`);
      }
    }
  }
  
  return hasTests;
}

async function testConfigurationFiles() {
  console.log('\n🔍 Testing Configuration Files...\n');
  
  const configFiles = [
    { path: 'package.json', checks: ['react', 'typescript', '@supabase'] },
    { path: 'tsconfig.json', checks: ['jsx', 'react'] },
    { path: 'jest.config.js', checks: ['jsdom', 'tsx'] }
  ];
  
  let allConfigsValid = true;
  
  for (const config of configFiles) {
    const exists = fileExists(config.path);
    console.log(`${exists ? '✅' : '❌'} ${config.path}: ${exists ? 'Found' : 'Missing'}`);
    
    if (exists) {
      const content = readFile(config.path);
      if (content) {
        config.checks.forEach(check => {
          const hasCheck = containsPattern(content, check);
          console.log(`   ${hasCheck ? '✅' : '❌'} Contains ${check}: ${hasCheck}`);
        });
      }
    } else {
      allConfigsValid = false;
    }
  }
  
  return allConfigsValid;
}

// Main test runner
async function runFrontendIntegrationTests() {
  console.log('🚀 Starting Frontend Components Integration Test Suite');
  console.log('='.repeat(60));
  
  const results = {};
  
  try {
    results.componentStructure = await testComponentStructure();
    results.typeDefinitions = await testTypeDefinitions();
    results.serviceIntegration = await testServiceIntegration();
    results.utilityFunctions = await testUtilityFunctions();
    results.componentIntegration = await testComponentIntegration();
    results.testFiles = await testTestFiles();
    results.configurationFiles = await testConfigurationFiles();
    
    console.log('\n📊 Test Summary:');
    console.log('='.repeat(30));
    
    Object.entries(results).forEach(([testName, result]) => {
      const status = result ? '✅ Pass' : '❌ Fail';
      const formattedName = testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      console.log(`${formattedName}: ${status}`);
    });
    
    const overallSuccess = Object.values(results).every(result => result);
    
    console.log('\n' + '='.repeat(60));
    console.log(`${overallSuccess ? '✅' : '❌'} Frontend integration test suite ${overallSuccess ? 'completed successfully' : 'completed with issues'}!`);
    
    if (overallSuccess) {
      console.log('\n🎉 All frontend components are properly structured and integrated!');
      console.log('   - React components are well-organized');
      console.log('   - TypeScript types are defined');
      console.log('   - Supabase integration is configured');
      console.log('   - Utility functions are available');
      console.log('   - Test files are present');
      console.log('   - Configuration files are set up');
    }
    
  } catch (error) {
    console.log(`❌ Test suite failed: ${error.message}`);
  }
}

// Run the tests
runFrontendIntegrationTests().catch(console.error);
