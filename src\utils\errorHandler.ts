/**
 * Centralized error handling utility
 */

import { log } from './logger';

// Error types
export enum ErrorType {
  VALIDATION = 'VALIDATION_ERROR',
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND_ERROR',
  DATABASE = 'DATABASE_ERROR',
  EXTERNAL_API = 'EXTERNAL_API_ERROR',
  FILE_PROCESSING = 'FILE_PROCESSING_ERROR',
  RATE_LIMIT = 'RATE_LIMIT_ERROR',
  INTERNAL = 'INTERNAL_ERROR',
}

// Custom error class
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly context?: any;

  constructor(
    message: string,
    type: ErrorType = ErrorType.INTERNAL,
    statusCode: number = 500,
    isOperational: boolean = true,
    context?: any
  ) {
    super(message);
    
    this.type = type;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.context = context;
    
    // Maintain proper stack trace
    Error.captureStackTrace(this, this.constructor);
  }
}

// Predefined error creators
export const createError = {
  validation: (message: string, context?: any) => 
    new AppError(message, ErrorType.VALIDATION, 400, true, context),
    
  authentication: (message: string = 'Authentication required', context?: any) => 
    new AppError(message, ErrorType.AUTHENTICATION, 401, true, context),
    
  authorization: (message: string = 'Insufficient permissions', context?: any) => 
    new AppError(message, ErrorType.AUTHORIZATION, 403, true, context),
    
  notFound: (resource: string = 'Resource', context?: any) => 
    new AppError(`${resource} not found`, ErrorType.NOT_FOUND, 404, true, context),
    
  database: (message: string, context?: any) => 
    new AppError(message, ErrorType.DATABASE, 500, true, context),
    
  externalApi: (service: string, message?: string, context?: any) => 
    new AppError(
      message || `External service ${service} unavailable`, 
      ErrorType.EXTERNAL_API, 
      502, 
      true, 
      { service, ...context }
    ),
    
  fileProcessing: (message: string, context?: any) => 
    new AppError(message, ErrorType.FILE_PROCESSING, 422, true, context),
    
  rateLimit: (message: string = 'Rate limit exceeded', context?: any) => 
    new AppError(message, ErrorType.RATE_LIMIT, 429, true, context),
    
  internal: (message: string = 'Internal server error', context?: any) => 
    new AppError(message, ErrorType.INTERNAL, 500, false, context),
};

// Error handler for async functions
export const asyncHandler = (fn: Function) => {
  return (req: any, res: any, next: any) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Error logger
export const logError = (error: Error | AppError, context?: any) => {
  const isAppError = error instanceof AppError;
  
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    type: isAppError ? error.type : ErrorType.INTERNAL,
    statusCode: isAppError ? error.statusCode : 500,
    isOperational: isAppError ? error.isOperational : false,
    context: isAppError ? error.context : context,
    timestamp: new Date().toISOString(),
  };
  
  if (isAppError && error.isOperational) {
    // Operational errors (expected errors)
    if (error.statusCode >= 500) {
      log.error('Operational error', errorInfo);
    } else {
      log.warn('Client error', errorInfo);
    }
  } else {
    // Programming errors (unexpected errors)
    log.error('Programming error', errorInfo);
  }
};

// Process uncaught exceptions and unhandled rejections
export const setupGlobalErrorHandlers = () => {
  process.on('uncaughtException', (error: Error) => {
    log.error('Uncaught Exception', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });
    
    // Graceful shutdown
    process.exit(1);
  });
  
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    log.error('Unhandled Rejection', {
      reason: reason instanceof Error ? reason.message : reason,
      stack: reason instanceof Error ? reason.stack : undefined,
      promise: promise.toString(),
      timestamp: new Date().toISOString(),
    });
    
    // Graceful shutdown
    process.exit(1);
  });
  
  // Graceful shutdown on SIGTERM
  process.on('SIGTERM', () => {
    log.info('SIGTERM received, shutting down gracefully');
    process.exit(0);
  });
  
  // Graceful shutdown on SIGINT
  process.on('SIGINT', () => {
    log.info('SIGINT received, shutting down gracefully');
    process.exit(0);
  });
  
  log.info('Global error handlers set up');
};

// Error response formatter
export const formatErrorResponse = (error: Error | AppError) => {
  const isAppError = error instanceof AppError;
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const response: any = {
    success: false,
    error: {
      type: isAppError ? error.type : ErrorType.INTERNAL,
      message: error.message,
      timestamp: new Date().toISOString(),
    },
  };
  
  // Add context in development or for operational errors
  if (isDevelopment || (isAppError && error.isOperational)) {
    if (isAppError && error.context) {
      response.error.context = error.context;
    }
  }
  
  // Add stack trace in development
  if (isDevelopment) {
    response.error.stack = error.stack;
  }
  
  return response;
};

// Validation helper
export const validateRequired = (data: any, fields: string[]) => {
  const missing = fields.filter(field => !data[field]);
  
  if (missing.length > 0) {
    throw createError.validation(
      `Missing required fields: ${missing.join(', ')}`,
      { missingFields: missing }
    );
  }
};

// Database error handler
export const handleDatabaseError = (error: any, operation: string) => {
  log.database(operation, 'unknown', { error: error.message });
  
  // Handle specific database errors
  if (error.code === '23505') { // Unique constraint violation
    throw createError.validation('Resource already exists', { 
      operation,
      constraint: error.constraint 
    });
  }
  
  if (error.code === '23503') { // Foreign key constraint violation
    throw createError.validation('Referenced resource does not exist', { 
      operation,
      constraint: error.constraint 
    });
  }
  
  if (error.code === '42P01') { // Table does not exist
    throw createError.database('Database schema error', { 
      operation,
      table: error.table 
    });
  }
  
  // Generic database error
  throw createError.database(`Database operation failed: ${operation}`, { 
    operation,
    originalError: error.message 
  });
};
