// Test script for Supabase Edge Functions
import https from 'https';
import fs from 'fs';

// Configuration from .env
const SUPABASE_URL = 'https://idvceughudfmupvoxgns.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlkdmNldWdodWRmbXVwdm94Z25zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MDgwMTEsImV4cCI6MjA2OTI4NDAxMX0.kMH7OCjSTW3mSml5QI45_-6NOnW1vXyCLZPHV6_pmmg';

const FUNCTIONS_BASE_URL = `${SUPABASE_URL}/functions/v1`;

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

// Test functions
async function testHealthEndpoints() {
  console.log('\n🔍 Testing Edge Function Health Endpoints...\n');
  
  const functions = ['upload-template', 'create-report', 'document-processor'];
  
  for (const func of functions) {
    try {
      console.log(`Testing ${func}...`);
      const url = `${FUNCTIONS_BASE_URL}/${func}`;
      
      const response = await makeRequest(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`✅ ${func}: Status ${response.status}`);
      if (response.data) {
        console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      }
    } catch (error) {
      console.log(`❌ ${func}: Error - ${error.message}`);
    }
    console.log('');
  }
}

async function testUploadTemplateFunction() {
  console.log('\n🔍 Testing upload-template Function...\n');
  
  try {
    const url = `${FUNCTIONS_BASE_URL}/upload-template`;
    
    // Test with minimal payload
    const testPayload = {
      templateName: 'Test Template',
      description: 'Test template for API testing'
    };
    
    const response = await makeRequest(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testPayload)
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ upload-template function is working');
    } else {
      console.log('⚠️ upload-template function returned non-success status');
    }
    
  } catch (error) {
    console.log(`❌ upload-template test failed: ${error.message}`);
  }
}

async function testCreateReportFunction() {
  console.log('\n🔍 Testing create-report Function...\n');
  
  try {
    const url = `${FUNCTIONS_BASE_URL}/create-report`;
    
    // Test with minimal payload
    const testPayload = {
      templateId: '12345678-1234-1234-1234-123456789012', // Mock UUID
      formData: {
        name: 'John Doe',
        email: '<EMAIL>',
        date: '2024-01-27'
      }
    };
    
    const response = await makeRequest(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testPayload)
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ create-report function is working');
    } else {
      console.log('⚠️ create-report function returned non-success status');
    }
    
  } catch (error) {
    console.log(`❌ create-report test failed: ${error.message}`);
  }
}

async function testDocumentProcessorFunction() {
  console.log('\n🔍 Testing document-processor Function...\n');
  
  try {
    const url = `${FUNCTIONS_BASE_URL}/document-processor`;
    
    // Test with minimal payload
    const testPayload = {
      documentId: '12345678-1234-1234-1234-123456789012', // Mock UUID
      action: 'process'
    };
    
    const response = await makeRequest(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testPayload)
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ document-processor function is working');
    } else {
      console.log('⚠️ document-processor function returned non-success status');
    }
    
  } catch (error) {
    console.log(`❌ document-processor test failed: ${error.message}`);
  }
}

async function testSupabaseAPI() {
  console.log('\n🔍 Testing Supabase REST API...\n');
  
  try {
    const url = `${SUPABASE_URL}/rest/v1/`;
    
    const response = await makeRequest(url, {
      method: 'GET',
      headers: {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      }
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200) {
      console.log('✅ Supabase REST API is accessible');
    } else {
      console.log('⚠️ Supabase REST API returned non-success status');
    }
    
  } catch (error) {
    console.log(`❌ Supabase API test failed: ${error.message}`);
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Supabase Edge Functions Test Suite');
  console.log('='.repeat(50));
  
  await testSupabaseAPI();
  await testHealthEndpoints();
  await testUploadTemplateFunction();
  await testCreateReportFunction();
  await testDocumentProcessorFunction();
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ Test suite completed!');
}

// Run the tests
runTests().catch(console.error);
