// Test script for Production Monitoring Dashboard
import fetch from 'node-fetch';

// Configuration
const config = {
  supabaseUrl: 'https://idvceughudfmupvoxgns.supabase.co',
  edgeFunctions: ['upload-template', 'create-report', 'document-processor'],
  timeout: 10000,
};

// Test Edge Functions monitoring
async function testEdgeFunctionMonitoring() {
  console.log('🔍 Testing Edge Functions Monitoring...\n');
  
  const results = {};
  
  for (const functionName of config.edgeFunctions) {
    console.log(`Testing ${functionName}...`);
    
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${config.supabaseUrl}/functions/v1/${functionName}`, {
        method: 'GET',
        timeout: config.timeout,
      });
      
      const responseTime = Date.now() - startTime;
      
      results[functionName] = {
        status: response.status,
        responseTime,
        accessible: true,
        error: null,
      };
      
      console.log(`  ✅ ${functionName}: ${response.status} (${responseTime}ms)`);
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      results[functionName] = {
        status: 0,
        responseTime,
        accessible: false,
        error: error.message,
      };
      
      console.log(`  ❌ ${functionName}: Error - ${error.message} (${responseTime}ms)`);
    }
  }
  
  return results;
}

// Test database connectivity
async function testDatabaseConnectivity() {
  console.log('\n🔍 Testing Database Connectivity...\n');
  
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${config.supabaseUrl}/rest/v1/`, {
      method: 'GET',
      headers: {
        'apikey': process.env.SUPABASE_ANON_KEY || '',
      },
      timeout: config.timeout,
    });
    
    const responseTime = Date.now() - startTime;
    
    console.log(`  ✅ Database: ${response.status} (${responseTime}ms)`);
    
    return {
      status: response.status,
      responseTime,
      accessible: true,
      error: null,
    };
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    console.log(`  ❌ Database: Error - ${error.message} (${responseTime}ms)`);
    
    return {
      status: 0,
      responseTime,
      accessible: false,
      error: error.message,
    };
  }
}

// Generate monitoring report
function generateMonitoringReport(edgeFunctionResults, databaseResult) {
  console.log('\n📊 Monitoring Report');
  console.log('='.repeat(50));
  
  // Overall status
  const allEdgeFunctionsAccessible = Object.values(edgeFunctionResults).every(result => result.accessible);
  const databaseAccessible = databaseResult.accessible;
  const overallStatus = allEdgeFunctionsAccessible && databaseAccessible;
  
  console.log(`Overall Status: ${overallStatus ? '✅ Healthy' : '❌ Issues Detected'}`);
  console.log('');
  
  // Edge Functions summary
  console.log('⚡ Edge Functions:');
  Object.entries(edgeFunctionResults).forEach(([functionName, result]) => {
    const statusIcon = result.accessible ? '✅' : '❌';
    const statusText = result.accessible ? `${result.status}` : 'Unreachable';
    console.log(`  ${statusIcon} ${functionName}: ${statusText} (${result.responseTime}ms)`);
    
    if (result.error) {
      console.log(`     Error: ${result.error}`);
    }
  });
  
  console.log('');
  
  // Database summary
  console.log('🗄️  Database:');
  const dbStatusIcon = databaseResult.accessible ? '✅' : '❌';
  const dbStatusText = databaseResult.accessible ? `${databaseResult.status}` : 'Unreachable';
  console.log(`  ${dbStatusIcon} Supabase REST API: ${dbStatusText} (${databaseResult.responseTime}ms)`);
  
  if (databaseResult.error) {
    console.log(`     Error: ${databaseResult.error}`);
  }
  
  console.log('');
  
  // Performance metrics
  console.log('📈 Performance Metrics:');
  
  const edgeFunctionResponseTimes = Object.values(edgeFunctionResults).map(r => r.responseTime);
  const avgEdgeFunctionResponseTime = edgeFunctionResponseTimes.reduce((a, b) => a + b, 0) / edgeFunctionResponseTimes.length;
  const maxEdgeFunctionResponseTime = Math.max(...edgeFunctionResponseTimes);
  
  console.log(`  Edge Functions Average Response Time: ${avgEdgeFunctionResponseTime.toFixed(0)}ms`);
  console.log(`  Edge Functions Max Response Time: ${maxEdgeFunctionResponseTime}ms`);
  console.log(`  Database Response Time: ${databaseResult.responseTime}ms`);
  
  console.log('');
  
  // Recommendations
  console.log('💡 Recommendations:');
  
  if (!overallStatus) {
    console.log('  - Investigate failed services immediately');
    console.log('  - Check Supabase project status and logs');
    console.log('  - Verify network connectivity');
  }
  
  if (avgEdgeFunctionResponseTime > 2000) {
    console.log('  - Edge function response times are high (>2s)');
    console.log('  - Consider optimizing function code or increasing resources');
  }
  
  if (databaseResult.responseTime > 1000) {
    console.log('  - Database response time is high (>1s)');
    console.log('  - Check database performance and query optimization');
  }
  
  if (overallStatus && avgEdgeFunctionResponseTime <= 2000 && databaseResult.responseTime <= 1000) {
    console.log('  - All services are healthy and performing well');
    console.log('  - Continue regular monitoring');
  }
  
  console.log('');
  
  // Monitoring dashboard info
  console.log('🔧 Monitoring Tools:');
  console.log('  - Run continuous monitoring: node monitor-production.js');
  console.log('  - Check Supabase logs: supabase functions logs <function-name>');
  console.log('  - View database logs: supabase db logs');
  console.log('  - Monitor application logs: tail -f logs/combined.log');
  
  return {
    overallStatus,
    edgeFunctions: edgeFunctionResults,
    database: databaseResult,
    metrics: {
      avgEdgeFunctionResponseTime,
      maxEdgeFunctionResponseTime,
      databaseResponseTime: databaseResult.responseTime,
    },
  };
}

// Main test function
async function runMonitoringTest() {
  console.log('🚀 Doc Maker App - Production Monitoring Test');
  console.log('='.repeat(60));
  console.log(`Supabase URL: ${config.supabaseUrl}`);
  console.log(`Test started: ${new Date().toISOString()}`);
  console.log('');
  
  try {
    // Test Edge Functions
    const edgeFunctionResults = await testEdgeFunctionMonitoring();
    
    // Test Database
    const databaseResult = await testDatabaseConnectivity();
    
    // Generate report
    const report = generateMonitoringReport(edgeFunctionResults, databaseResult);
    
    // Save report to file
    const reportData = {
      timestamp: new Date().toISOString(),
      ...report,
    };
    
    const fs = await import('fs');
    const logsDir = 'logs';
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      `${logsDir}/monitoring-test-${Date.now()}.json`,
      JSON.stringify(reportData, null, 2)
    );
    
    console.log(`📄 Report saved to logs/monitoring-test-${Date.now()}.json`);
    
    return report.overallStatus;
    
  } catch (error) {
    console.error('❌ Monitoring test failed:', error.message);
    return false;
  }
}

// Run the test
runMonitoringTest()
  .then(success => {
    console.log('\n' + '='.repeat(60));
    console.log(`${success ? '✅' : '❌'} Monitoring test ${success ? 'completed successfully' : 'completed with issues'}!`);
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  });
