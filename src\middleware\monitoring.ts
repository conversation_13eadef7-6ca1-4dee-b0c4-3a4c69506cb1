/**
 * Monitoring and metrics middleware
 */

import { Request, Response, NextFunction } from 'express';
import { log } from '../utils/logger';

// Request timing middleware
export const requestTiming = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // Override res.end to capture response time
  const originalEnd = res.end.bind(res);
  res.end = function(chunk?: any, encoding?: any, cb?: any) {
    const responseTime = Date.now() - startTime;

    // Log the request
    log.request(req, res, responseTime);

    // Call original end method
    return originalEnd(chunk, encoding, cb);
  } as any;
  
  next();
};

// Health check metrics
interface HealthMetrics {
  uptime: number;
  timestamp: string;
  memory: NodeJS.MemoryUsage;
  cpu: any;
  requests: {
    total: number;
    errors: number;
    averageResponseTime: number;
  };
  database: {
    connected: boolean;
    responseTime?: number;
  };
  storage: {
    available: boolean;
    responseTime?: number;
  };
}

// Simple metrics store (in production, use Redis or similar)
const metrics = {
  requests: {
    total: 0,
    errors: 0,
    responseTimes: [] as number[],
  },
  database: {
    connected: true,
    lastCheck: Date.now(),
    responseTime: 0,
  },
  storage: {
    available: true,
    lastCheck: Date.now(),
    responseTime: 0,
  },
};

// Metrics collection middleware
export const collectMetrics = (_req: Request, res: Response, next: NextFunction) => {
  metrics.requests.total++;

  const startTime = Date.now();

  // Override res.end to capture metrics
  const originalEnd = res.end.bind(res);
  res.end = function(chunk?: any, encoding?: any, cb?: any) {
    const responseTime = Date.now() - startTime;
    metrics.requests.responseTimes.push(responseTime);

    // Keep only last 1000 response times
    if (metrics.requests.responseTimes.length > 1000) {
      metrics.requests.responseTimes = metrics.requests.responseTimes.slice(-1000);
    }

    // Count errors
    if (res.statusCode >= 400) {
      metrics.requests.errors++;
    }

    // Call original end method
    return originalEnd(chunk, encoding, cb);
  } as any;

  next();
};

// Health check endpoint handler
export const healthCheck = async (_req: Request, res: Response) => {
  try {
    const healthMetrics: HealthMetrics = {
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      requests: {
        total: metrics.requests.total,
        errors: metrics.requests.errors,
        averageResponseTime: metrics.requests.responseTimes.length > 0
          ? metrics.requests.responseTimes.reduce((a, b) => a + b, 0) / metrics.requests.responseTimes.length
          : 0,
      },
      database: {
        connected: metrics.database.connected,
        responseTime: metrics.database.responseTime,
      },
      storage: {
        available: metrics.storage.available,
        responseTime: metrics.storage.responseTime,
      },
    };
    
    // Log health check
    log.info('Health check requested', {
      uptime: healthMetrics.uptime,
      memoryUsage: healthMetrics.memory.heapUsed,
      totalRequests: healthMetrics.requests.total,
      errorRate: (healthMetrics.requests.errors / healthMetrics.requests.total) * 100,
    });
    
    res.status(200).json({
      status: 'OK',
      service: 'Doc Maker App',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      ...healthMetrics,
    });
    
  } catch (error) {
    log.error('Health check failed', { error: error instanceof Error ? error.message : 'Unknown error' });
    
    res.status(503).json({
      status: 'ERROR',
      service: 'Doc Maker App',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
};

// Error tracking middleware
export const errorTracking = (error: Error, req: Request, res: Response, _next: NextFunction) => {
  // Log the error with context
  log.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    body: req.body,
    params: req.params,
    query: req.query,
  });
  
  // Increment error count
  metrics.requests.errors++;
  
  // Send error response
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(500).json({
    error: 'Internal Server Error',
    message: isDevelopment ? error.message : 'Something went wrong',
    timestamp: new Date().toISOString(),
    ...(isDevelopment && { stack: error.stack }),
  });
};

// Performance monitoring
export const performanceMonitor = {
  start: (operation: string) => {
    return {
      operation,
      startTime: Date.now(),
      end: function(meta?: any) {
        const duration = Date.now() - this.startTime;
        log.performance(this.operation, duration, meta);
        return duration;
      },
    };
  },
};

// Database health check
export const checkDatabaseHealth = async () => {
  const startTime = Date.now();
  
  try {
    // This would be replaced with actual database ping
    // For now, simulate a database check
    await new Promise(resolve => setTimeout(resolve, 10));
    
    const responseTime = Date.now() - startTime;
    metrics.database.connected = true;
    metrics.database.responseTime = responseTime;
    metrics.database.lastCheck = Date.now();
    
    return { connected: true, responseTime };
  } catch (error) {
    log.error('Database health check failed', { error: error instanceof Error ? error.message : 'Unknown error' });
    metrics.database.connected = false;
    metrics.database.lastCheck = Date.now();
    
    return { connected: false, responseTime: 0 };
  }
};

// Storage health check
export const checkStorageHealth = async () => {
  const startTime = Date.now();
  
  try {
    // This would be replaced with actual storage ping
    // For now, simulate a storage check
    await new Promise(resolve => setTimeout(resolve, 5));
    
    const responseTime = Date.now() - startTime;
    metrics.storage.available = true;
    metrics.storage.responseTime = responseTime;
    metrics.storage.lastCheck = Date.now();
    
    return { available: true, responseTime };
  } catch (error) {
    log.error('Storage health check failed', { error: error instanceof Error ? error.message : 'Unknown error' });
    metrics.storage.available = false;
    metrics.storage.lastCheck = Date.now();
    
    return { available: false, responseTime: 0 };
  }
};

// Periodic health checks
export const startPeriodicHealthChecks = () => {
  const interval = 60000; // 1 minute
  
  setInterval(async () => {
    await checkDatabaseHealth();
    await checkStorageHealth();
    
    log.info('Periodic health check completed', {
      database: metrics.database,
      storage: metrics.storage,
      requests: {
        total: metrics.requests.total,
        errors: metrics.requests.errors,
        errorRate: (metrics.requests.errors / metrics.requests.total) * 100,
      },
    });
  }, interval);
  
  log.info('Periodic health checks started', { interval: `${interval}ms` });
};
